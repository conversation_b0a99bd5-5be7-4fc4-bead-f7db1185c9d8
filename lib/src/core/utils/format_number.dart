String formatNumber(double number) {
  // Check if the number has meaningful decimal places
  if (number == number.roundToDouble()) {
    // No decimal places needed, format with commas
    return _addCommas(number.toStringAsFixed(0));
  } else {
    // Has decimal places, format to 2 decimal places with commas
    return _addCommas(number.toStringAsFixed(2));
  }
}

String _addCommas(String numberString) {
  // Split by decimal point if exists
  List<String> parts = numberString.split('.');
  String integerPart = parts[0];
  String decimalPart = parts.length > 1 ? '.${parts[1]}' : '';

  // Add commas to integer part
  String result = '';
  for (int i = 0; i < integerPart.length; i++) {
    if (i > 0 && (integerPart.length - i) % 3 == 0) {
      result += ',';
    }
    result += integerPart[i];
  }

  return result + decimalPart;
}
