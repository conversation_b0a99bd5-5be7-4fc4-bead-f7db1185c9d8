class ApiEndpoints {
  static const String url = 'http://app.iv4it.com';
  static const String baseUrl = '$url/api/v2.0/emp/portal';

  //? Auth
  static const String login = '$baseUrl/login';

  //? APIs
  static const String home = '$baseUrl/dashboard';
  static const String attendance = '$baseUrl/insert/attendance';
  static const String addReply = '$baseUrl/cl/cp/ticket/add/thread';
  static const String addTicket = '$baseUrl/cl/cp/ticket/cr/act/save';

  //? Profile
  static const String profile = '$baseUrl/profile';
  static const String profileUpdate = '$baseUrl/profile/update';

  //? Leave Management
  static const String leaveList = '$baseUrl/leave/list';
  static const String leaveAdd = '$baseUrl/leave/add/request';
  static const String leaveUpdate = '$baseUrl/leave/update/request';
  static const String leaveDelete = '$baseUrl/leave/delete/request';

  //? Client Management
  static const String clientsList = '$baseUrl/clients/list';
  static const String clientAdd = '$baseUrl/clients/add';

  //? Agreements/Quotations Management
  static const String quotationsList = '$baseUrl/agreements/qc/list';
  static const String quotationSave = '$baseUrl/agreements/qc/save';
  static const String quotationUpdate = '$baseUrl/agreements/qc/update';
  static const String quotationDetails = '$baseUrl/agreements/qc/view';
  static const String quotationAssignPrice =
      '$baseUrl/agreements/qc/assign-price';
  static const String quotationStatusUpdate =
      '$baseUrl/agreements/qc/status-update';
  static const String quotationPrint = '$baseUrl/agreements/qc/extract-pdf';

  //? Licenses Management
  static const String licensesList = '$baseUrl/agreements/lc/list';

  //? Products Management
  static const String productsList = '$baseUrl/fetch/available/prod_serv';

  static String ticketDetails(int? ticketId) =>
      '$baseUrl/cl/cp/ticket/fetch/details?tid=$ticketId';

  static String ticketReports(String? startDate, String? endDate) =>
      '$baseUrl/cl/cp/tickets/report?from_date=$startDate&to_date=$endDate';
}
