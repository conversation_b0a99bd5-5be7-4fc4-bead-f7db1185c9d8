import 'package:equatable/equatable.dart';

class ProductListModel extends Equatable {
  final bool success;
  final ProductData data;

  const ProductListModel({
    this.success = false,
    this.data = const ProductData(),
  });

  factory ProductListModel.fromJson(Map<String, dynamic> json) {
    return ProductListModel(
      success: json['success'] ?? false,
      data: json['data'] != null
          ? ProductData.fromJson(json['data'])
          : const ProductData(),
    );
  }

  factory ProductListModel.empty() => const ProductListModel();

  @override
  List<Object?> get props => [success, data];
}

class ProductData extends Equatable {
  final List<Product> categoryLists;

  const ProductData({
    this.categoryLists = const [],
  });

  factory ProductData.fromJson(Map<String, dynamic> json) {
    return ProductData(
      categoryLists: (json['category_lists'] as List<dynamic>?)
              ?.map((e) => Product.fromJson(e))
              .toList() ??
          [],
    );
  }

  @override
  List<Object?> get props => [categoryLists];
}

class Product extends Equatable {
  final int productId;
  final String productName;
  final int hasSubList;
  final int hasLimitUser;
  final String productPic;
  final List<SubProduct> subProducts;

  const Product({
    this.productId = 0,
    this.productName = '',
    this.hasSubList = 0,
    this.hasLimitUser = 0,
    this.productPic = '',
    this.subProducts = const [],
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      productId: json['product_id'] ?? 0,
      productName: json['product_name'] ?? '',
      hasSubList: json['has_sub_list'] ?? 0,
      hasLimitUser: json['hasLimitUser'] ?? 0,
      productPic: json['product_pic'] ?? '',
      subProducts: (json['sub_products'] as List<dynamic>?)
              ?.map((e) => SubProduct.fromJson(e))
              .toList() ??
          [],
    );
  }

  bool get hasSubProducts => hasSubList == 1;
  bool get hasUserLimit => hasLimitUser == 1;

  @override
  List<Object?> get props =>
      [productId, productName, hasSubList, hasLimitUser, productPic, subProducts];
}

class SubProduct extends Equatable {
  final int subProductId;
  final String subProductName;
  final String subProductCost;

  const SubProduct({
    this.subProductId = 0,
    this.subProductName = '',
    this.subProductCost = '',
  });

  factory SubProduct.fromJson(Map<String, dynamic> json) {
    return SubProduct(
      subProductId: json['sub_product_id'] ?? 0,
      subProductName: json['sub_product_name'] ?? '',
      subProductCost: json['sub_product_cost'] ?? '',
    );
  }

  @override
  List<Object?> get props => [subProductId, subProductName, subProductCost];
}

// Helper class for multi-selection
class SelectedProductDetail extends Equatable {
  final Product product;
  final List<SubProduct> selectedSubProducts;
  final int userLimit;

  const SelectedProductDetail({
    required this.product,
    this.selectedSubProducts = const [],
    this.userLimit = 0,
  });

  SelectedProductDetail copyWith({
    Product? product,
    List<SubProduct>? selectedSubProducts,
    int? userLimit,
  }) {
    return SelectedProductDetail(
      product: product ?? this.product,
      selectedSubProducts: selectedSubProducts ?? this.selectedSubProducts,
      userLimit: userLimit ?? this.userLimit,
    );
  }

  @override
  List<Object?> get props => [product, selectedSubProducts, userLimit];
}
