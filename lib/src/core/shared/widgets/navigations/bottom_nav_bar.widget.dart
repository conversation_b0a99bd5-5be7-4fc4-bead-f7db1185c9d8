import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/screens/clients/view/clients_screen.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_screen.dart';
import 'package:opti_tickets/src/screens/main_screen/view/main_screen.dart';
import 'package:opti_tickets/src/screens/profile/view/profile_screen.dart';
import 'package:opti_tickets/src/screens/quotations/view/quotations_screen.dart';
import 'package:opti_tickets/src/screens/services/view/services_screen.dart';
import 'package:opti_tickets/src/screens/settings/integrated_settings_screen.dart';

import '../../../theme/color_manager.dart';
import 'controller/bottom_nav_bar.controller.dart';

class BottomNavBarWidget extends ConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(bottomNavigationControllerProvider);

    return BottomNavigationBar(
      currentIndex: selectedIndex,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: ColorManager.primaryColor,
      unselectedItemColor: ColorManager.darkGrey,
      backgroundColor: Colors.white,
      elevation: 8,
      onTap: (index) {
        ref
            .read(bottomNavigationControllerProvider.notifier)
            .changeIndex(index);
        _navigateToPage(context, index);
      },
      items: [
        BottomNavigationBarItem(
          icon: Icon(
            CupertinoIcons.home,
            color: selectedIndex == 0
                ? ColorManager.primaryColor
                : ColorManager.darkGrey,
          ),
          label: context.tr.home,
        ),
        BottomNavigationBarItem(
          icon: Icon(
            CupertinoIcons.person,
            color: selectedIndex == 1
                ? ColorManager.primaryColor
                : ColorManager.darkGrey,
          ),
          label: context.tr.myProfile,
        ),
        BottomNavigationBarItem(
          icon: Icon(
            CupertinoIcons.settings,
            color: selectedIndex == 2
                ? ColorManager.primaryColor
                : ColorManager.darkGrey,
          ),
          label: context.tr.services,
        ),
        BottomNavigationBarItem(
          icon: Icon(
            CupertinoIcons.money_dollar,
            color: selectedIndex == 3
                ? ColorManager.primaryColor
                : ColorManager.darkGrey,
          ),
          label: context.tr.sales,
        ),
      ],
    );
  }

  void _navigateToPage(BuildContext context, int index) {
    switch (index) {
      case 0:
        // Home
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const MainScreen()),
          (route) => false,
        );
        break;
      case 1:
        // Quotations
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
              builder: (context) => const IntegratedSettingsScreen()),
          (route) => false,
        );
        break;
      case 2:
        // Quotations
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const ServicesScreen()),
          (route) => false,
        );
        break;
        // case 3:
        //   // Clients
        //   Navigator.pushAndRemoveUntil(
        //     context,
        //     MaterialPageRoute(builder: (context) => const ClientsScreen()),
        //     (route) => false,
        //   );
        //   break;
        // case 4:
        //   // Settings
        //   Navigator.pushAndRemoveUntil(
        //     context,
        //     MaterialPageRoute(
        //         builder: (context) => const IntegratedSettingsScreen()),
        //     (route) => false,
        //   );
        break;
    }
  }
}
