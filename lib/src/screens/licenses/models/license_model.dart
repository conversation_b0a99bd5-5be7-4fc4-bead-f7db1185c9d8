import 'package:equatable/equatable.dart';

class LicenseListModel extends Equatable {
  final bool success;
  final LicensePermissions can;
  final List<License> licenses;

  const LicenseListModel({
    this.success = false,
    this.can = const LicensePermissions(),
    this.licenses = const [],
  });

  factory LicenseListModel.fromJson(Map<String, dynamic> json) {
    return LicenseListModel(
      success: json['success'] ?? false,
      can: LicensePermissions.fromJson(json['can'] ?? {}),
      licenses: (json['dt'] as List<dynamic>?)
              ?.map((e) => License.fromJson(e))
              .toList() ??
          [],
    );
  }

  factory LicenseListModel.empty() => const LicenseListModel();

  @override
  List<Object?> get props => [success, can, licenses];
}

class LicensePermissions extends Equatable {
  final bool add;

  const LicensePermissions({
    this.add = false,
  });

  factory LicensePermissions.fromJson(Map<String, dynamic> json) {
    return LicensePermissions(
      add: json['add'] ?? false,
    );
  }

  @override
  List<Object?> get props => [add];
}

class License extends Equatable {
  final int ipd;
  final String unicode;
  final String createat;
  final String client;
  final Map<String, List<LicenseSubscription>> subscriptions;
  final LicenseStatus status;

  const License({
    this.ipd = 0,
    this.unicode = '',
    this.createat = '',
    this.client = '',
    this.subscriptions = const {},
    this.status = const LicenseStatus(),
  });

  factory License.fromJson(Map<String, dynamic> json) {
    Map<String, List<LicenseSubscription>> subscriptionsMap = {};

    if (json['subscriptions'] != null) {
      final subscriptionsData = json['subscriptions'] as Map<String, dynamic>;
      subscriptionsData.forEach((key, value) {
        if (value is List) {
          subscriptionsMap[key] =
              value.map((e) => LicenseSubscription.fromJson(e)).toList();
        }
      });
    }

    return License(
      ipd: json['ipd'] ?? 0,
      unicode: json['unicode'] ?? '',
      createat: json['createat'] ?? '',
      client: json['client'] ?? '',
      subscriptions: subscriptionsMap,
      status: LicenseStatus.fromJson(json['status'] ?? {}),
    );
  }

  @override
  List<Object?> get props =>
      [ipd, unicode, createat, client, subscriptions, status];
}

class LicenseSubscription extends Equatable {
  final String serviceName;
  final String subserviceName;

  const LicenseSubscription({
    this.serviceName = '',
    this.subserviceName = '',
  });

  factory LicenseSubscription.fromJson(Map<String, dynamic> json) {
    return LicenseSubscription(
      serviceName: json['service_name'] ?? '',
      subserviceName: json['subservice_name'] ?? '',
    );
  }

  @override
  List<Object?> get props => [serviceName, subserviceName];
}

class LicenseStatus extends Equatable {
  final int id;
  final String status;
  final String color;
  final String? message;

  const LicenseStatus({
    this.id = 0,
    this.status = '',
    this.color = '',
    this.message,
  });

  factory LicenseStatus.fromJson(Map<String, dynamic> json) {
    return LicenseStatus(
      id: json['id'] ?? 0,
      status: json['status'] ?? '',
      color: json['color'] ?? '',
      message: json['message'],
    );
  }

  @override
  List<Object?> get props => [id, status, color, message];
}

// License Detail Models
class LicenseDetailModel extends Equatable {
  final bool success;
  final LicenseDetailPermissions can;
  final LicenseDetailData dt;
  final dynamic action;

  const LicenseDetailModel({
    this.success = false,
    this.can = const LicenseDetailPermissions(),
    this.dt = const LicenseDetailData(),
    this.action,
  });

  factory LicenseDetailModel.fromJson(Map<String, dynamic> json) {
    return LicenseDetailModel(
      success: json['success'] ?? false,
      can: LicenseDetailPermissions.fromJson(json['can'] ?? {}),
      dt: LicenseDetailData.fromJson(json['dt'] ?? {}),
      action: json['action'],
    );
  }

  factory LicenseDetailModel.empty() => const LicenseDetailModel();

  @override
  List<Object?> get props => [success, can, dt, action];
}

class LicenseDetailPermissions extends Equatable {
  final bool edit;
  final bool price;
  final bool print;
  final bool send;

  const LicenseDetailPermissions({
    this.edit = false,
    this.price = false,
    this.print = false,
    this.send = false,
  });

  factory LicenseDetailPermissions.fromJson(Map<String, dynamic> json) {
    return LicenseDetailPermissions(
      edit: json['edit'] ?? false,
      price: json['price'] ?? false,
      print: json['print'] ?? false,
      send: json['send'] ?? false,
    );
  }

  @override
  List<Object?> get props => [edit, price, print, send];
}

class LicenseDetailData extends Equatable {
  final int ipd;
  final String unicode;
  final String createat;
  final LicenseClient client;
  final List<LicenseService> subs;
  final String discount;
  final String payStartdate;
  final String payEvery;
  final String totalInstallments;
  final List<LicenseInstallment> installments;
  final LicenseDetailStatus status;
  final String createdAt;
  final String createdBy;
  final String approvedAt;
  final String approvedBy;
  final bool hasSignedFile;
  final List<LicenseHistory> history;

  const LicenseDetailData({
    this.ipd = 0,
    this.unicode = '',
    this.createat = '',
    this.client = const LicenseClient(),
    this.subs = const [],
    this.discount = '',
    this.payStartdate = '',
    this.payEvery = '',
    this.totalInstallments = '',
    this.installments = const [],
    this.status = const LicenseDetailStatus(),
    this.createdAt = '',
    this.createdBy = '',
    this.approvedAt = '',
    this.approvedBy = '',
    this.hasSignedFile = false,
    this.history = const [],
  });

  factory LicenseDetailData.fromJson(Map<String, dynamic> json) {
    return LicenseDetailData(
      ipd: json['ipd'] ?? 0,
      unicode: json['unicode'] ?? '',
      createat: json['createat'] ?? '',
      client: LicenseClient.fromJson(json['client'] ?? {}),
      subs: (json['subs'] as List<dynamic>?)
              ?.map((e) => LicenseService.fromJson(e))
              .toList() ??
          [],
      discount: json['discount'] ?? '',
      payStartdate: json['pay_startdate'] ?? '',
      payEvery: json['pay_every'] ?? '',
      totalInstallments: json['total_installments'] ?? '',
      installments: (json['installments'] as List<dynamic>?)
              ?.map((e) => LicenseInstallment.fromJson(e))
              .toList() ??
          [],
      status: LicenseDetailStatus.fromJson(json['status'] ?? {}),
      createdAt: json['createdAt'] ?? '',
      createdBy: json['createdBy'] ?? '',
      approvedAt: json['ApprovedAt'] ?? '',
      approvedBy: json['ApprovedBy'] ?? '',
      hasSignedFile: json['has_SignedFile'] ?? false,
      history: (json['history'] as List<dynamic>?)
              ?.map((e) => LicenseHistory.fromJson(e))
              .toList() ??
          [],
    );
  }

  @override
  List<Object?> get props => [
        ipd,
        unicode,
        createat,
        client,
        subs,
        discount,
        payStartdate,
        payEvery,
        totalInstallments,
        installments,
        status,
        createdAt,
        createdBy,
        approvedAt,
        approvedBy,
        hasSignedFile,
        history,
      ];
}

class LicenseClient extends Equatable {
  final int cid;
  final String cName;
  final String cPerson;
  final String cAddress;

  const LicenseClient({
    this.cid = 0,
    this.cName = '',
    this.cPerson = '',
    this.cAddress = '',
  });

  factory LicenseClient.fromJson(Map<String, dynamic> json) {
    return LicenseClient(
      cid: json['cid'] ?? 0,
      cName: json['c_name'] ?? '',
      cPerson: json['c_person'] ?? '',
      cAddress: json['c_address'] ?? '',
    );
  }

  @override
  List<Object?> get props => [cid, cName, cPerson, cAddress];
}

class LicenseService extends Equatable {
  final int serviceId;
  final String serviceName;
  final bool serviceHasLimitUser;
  final String limitUser;
  final String extraUserCost;
  final int vat;
  final List<LicenseSubservice> subservices;

  const LicenseService({
    this.serviceId = 0,
    this.serviceName = '',
    this.serviceHasLimitUser = false,
    this.limitUser = '',
    this.extraUserCost = '',
    this.vat = 0,
    this.subservices = const [],
  });

  factory LicenseService.fromJson(Map<String, dynamic> json) {
    return LicenseService(
      serviceId: json['service_id'] ?? 0,
      serviceName: json['service_name'] ?? '',
      serviceHasLimitUser: json['service_hasLimitUser'] ?? false,
      limitUser: json['limitUser'] ?? '',
      extraUserCost: json['extraUserCost'] ?? '',
      vat: json['vat'] ?? 0,
      subservices: (json['subservices'] as List<dynamic>?)
              ?.map((e) => LicenseSubservice.fromJson(e))
              .toList() ??
          [],
    );
  }

  @override
  List<Object?> get props => [
        serviceId,
        serviceName,
        serviceHasLimitUser,
        limitUser,
        extraUserCost,
        vat,
        subservices,
      ];
}

class LicenseSubservice extends Equatable {
  final int subserviceId;
  final String subserviceName;
  final double subserviceCost;

  const LicenseSubservice({
    this.subserviceId = 0,
    this.subserviceName = '',
    this.subserviceCost = 0.0,
  });

  factory LicenseSubservice.fromJson(Map<String, dynamic> json) {
    return LicenseSubservice(
      subserviceId: json['subservice_id'] ?? 0,
      subserviceName: json['subservice_name'] ?? '',
      subserviceCost: (json['subservice_cost'] ?? 0).toDouble(),
    );
  }

  @override
  List<Object?> get props => [subserviceId, subserviceName, subserviceCost];
}

class LicenseInstallment extends Equatable {
  final int ccpayId;
  final int ccpayCcID;
  final String? ccpayTitleE;
  final String? ccpayTitleA;
  final String ccpayInstallNo;
  final String ccpayInstallDue;
  final String ccpayInstallAmount;
  final String createdAt;
  final String updatedAt;

  const LicenseInstallment({
    this.ccpayId = 0,
    this.ccpayCcID = 0,
    this.ccpayTitleE,
    this.ccpayTitleA,
    this.ccpayInstallNo = '',
    this.ccpayInstallDue = '',
    this.ccpayInstallAmount = '',
    this.createdAt = '',
    this.updatedAt = '',
  });

  factory LicenseInstallment.fromJson(Map<String, dynamic> json) {
    return LicenseInstallment(
      ccpayId: json['ccpay_id'] ?? 0,
      ccpayCcID: json['ccpay_ccID'] ?? 0,
      ccpayTitleE: json['ccpay_titleE'],
      ccpayTitleA: json['ccpay_titleA'],
      ccpayInstallNo: json['ccpay_installNo'] ?? '',
      ccpayInstallDue: json['ccpay_installDue'] ?? '',
      ccpayInstallAmount: json['ccpay_installAmount'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  @override
  List<Object?> get props => [
        ccpayId,
        ccpayCcID,
        ccpayTitleE,
        ccpayTitleA,
        ccpayInstallNo,
        ccpayInstallDue,
        ccpayInstallAmount,
        createdAt,
        updatedAt,
      ];
}

class LicenseDetailStatus extends Equatable {
  final String name;
  final String message;
  final String by;

  const LicenseDetailStatus({
    this.name = '',
    this.message = '',
    this.by = '',
  });

  factory LicenseDetailStatus.fromJson(Map<String, dynamic> json) {
    return LicenseDetailStatus(
      name: json['name'] ?? '',
      message: json['message'] ?? '',
      by: json['by'] ?? '',
    );
  }

  @override
  List<Object?> get props => [name, message, by];
}

class LicenseHistory extends Equatable {
  final String historyDetail;
  final String cchColor;
  final String createdAt;
  final String createdBy;

  const LicenseHistory({
    this.historyDetail = '',
    this.cchColor = '',
    this.createdAt = '',
    this.createdBy = '',
  });

  factory LicenseHistory.fromJson(Map<String, dynamic> json) {
    return LicenseHistory(
      historyDetail: json['history_detail'] ?? '',
      cchColor: json['cch_color'] ?? '',
      createdAt: json['created_at'] ?? '',
      createdBy: json['created_by'] ?? '',
    );
  }

  @override
  List<Object?> get props => [historyDetail, cchColor, createdAt, createdBy];
}
