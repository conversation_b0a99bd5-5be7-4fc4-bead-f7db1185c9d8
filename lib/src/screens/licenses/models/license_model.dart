import 'package:equatable/equatable.dart';

class LicenseListModel extends Equatable {
  final bool success;
  final LicensePermissions can;
  final List<License> licenses;

  const LicenseListModel({
    this.success = false,
    this.can = const LicensePermissions(),
    this.licenses = const [],
  });

  factory LicenseListModel.fromJson(Map<String, dynamic> json) {
    return LicenseListModel(
      success: json['success'] ?? false,
      can: LicensePermissions.fromJson(json['can'] ?? {}),
      licenses: (json['dt'] as List<dynamic>?)
              ?.map((e) => License.fromJson(e))
              .toList() ??
          [],
    );
  }

  factory LicenseListModel.empty() => const LicenseListModel();

  @override
  List<Object?> get props => [success, can, licenses];
}

class LicensePermissions extends Equatable {
  final bool add;

  const LicensePermissions({
    this.add = false,
  });

  factory LicensePermissions.fromJson(Map<String, dynamic> json) {
    return LicensePermissions(
      add: json['add'] ?? false,
    );
  }

  @override
  List<Object?> get props => [add];
}

class License extends Equatable {
  final int ipd;
  final String unicode;
  final String createat;
  final String client;
  final Map<String, List<LicenseSubscription>> subscriptions;
  final LicenseStatus status;

  const License({
    this.ipd = 0,
    this.unicode = '',
    this.createat = '',
    this.client = '',
    this.subscriptions = const {},
    this.status = const LicenseStatus(),
  });

  factory License.fromJson(Map<String, dynamic> json) {
    Map<String, List<LicenseSubscription>> subscriptionsMap = {};
    
    if (json['subscriptions'] != null) {
      final subscriptionsData = json['subscriptions'] as Map<String, dynamic>;
      subscriptionsData.forEach((key, value) {
        if (value is List) {
          subscriptionsMap[key] = value
              .map((e) => LicenseSubscription.fromJson(e))
              .toList();
        }
      });
    }

    return License(
      ipd: json['ipd'] ?? 0,
      unicode: json['unicode'] ?? '',
      createat: json['createat'] ?? '',
      client: json['client'] ?? '',
      subscriptions: subscriptionsMap,
      status: LicenseStatus.fromJson(json['status'] ?? {}),
    );
  }

  @override
  List<Object?> get props => [ipd, unicode, createat, client, subscriptions, status];
}

class LicenseSubscription extends Equatable {
  final String serviceName;
  final String subserviceName;

  const LicenseSubscription({
    this.serviceName = '',
    this.subserviceName = '',
  });

  factory LicenseSubscription.fromJson(Map<String, dynamic> json) {
    return LicenseSubscription(
      serviceName: json['service_name'] ?? '',
      subserviceName: json['subservice_name'] ?? '',
    );
  }

  @override
  List<Object?> get props => [serviceName, subserviceName];
}

class LicenseStatus extends Equatable {
  final int id;
  final String status;
  final String color;
  final String? message;

  const LicenseStatus({
    this.id = 0,
    this.status = '',
    this.color = '',
    this.message,
  });

  factory LicenseStatus.fromJson(Map<String, dynamic> json) {
    return LicenseStatus(
      id: json['id'] ?? 0,
      status: json['status'] ?? '',
      color: json['color'] ?? '',
      message: json['message'],
    );
  }

  @override
  List<Object?> get props => [id, status, color, message];
}
