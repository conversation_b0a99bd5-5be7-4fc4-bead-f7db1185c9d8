import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';

import 'package:http/http.dart' as http;
import 'package:opti_tickets/src/core/consts/network/api_endpoints.dart';
import 'package:opti_tickets/src/screens/licenses/models/license_model.dart';
import 'package:xr_helper/xr_helper.dart';

class LicenseRepository with BaseRepository {
  final BaseApiServices networkApiService;

  LicenseRepository({
    required this.networkApiService,
  });

  // * Get License List
  Future<LicenseListModel> getLicenseList() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.licensesList;

        final response = await networkApiService.getResponse(url);

        if (response == null) {
          return LicenseListModel.empty();
        }

        final licenseListModel = LicenseListModel.fromJson(response);

        return licenseListModel;
      },
    );
  }

  // * Get License Details
  Future<LicenseDetailModel> getLicenseDetails({
    required int licenseId,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.licenseDetails;

        final response = await networkApiService.postResponse(url, body: {
          'lc_id': licenseId,
        });

        if (response == null) {
          return LicenseDetailModel.empty();
        }

        final licenseDetailModel = LicenseDetailModel.fromJson(response);

        return licenseDetailModel;
      },
    );
  }

  // * Update License Status
  Future<bool> updateLicenseStatus({
    required String lcId,
    required String statusId,
    String reason = '',
    String? attachmentPath,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.licenseStatusUpdate;

        final body = {
          'lc_id': lcId,
          'status_id': statusId,
          'reason': reason,
          'attachment': '',
        };

        final filePaths =
            attachmentPath != null ? [attachmentPath] : <String>[];

        final res = await networkApiService.postResponse(
          url,
          body: body,
          fieldName: 'attachment',
          filePaths: filePaths,
        );

        log('License Status Update Response: $res');
        return true;
      },
    );
  }

  // * Print License
  Future<Uint8List?> printLicense({
    required int licenseId,
    required String language,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.licensePrint;

        final body = {
          'lc_id': licenseId.toString(),
          'lc_lang': language,
          'lc_action': 'd', // Use 'd' for download
        };

        final res = await http.post(
          Uri.parse(url),
          body: jsonEncode(body),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/pdf',
            'Authorization':
                "Bearer ${GetStorageService.getData(key: LocalKeys.token)}",
          },
        );

        log('Print License Response Status: ${res.statusCode}');

        if (res.statusCode == 200) {
          // Return the PDF bytes
          return res.bodyBytes;
        } else {
          log('Print License Error: ${res.body}');
          return null;
        }
      },
    );
  }
}
