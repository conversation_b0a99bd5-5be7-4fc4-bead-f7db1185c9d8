import 'package:opti_tickets/src/core/consts/network/api_endpoints.dart';
import 'package:opti_tickets/src/screens/licenses/models/license_model.dart';
import 'package:xr_helper/xr_helper.dart';

class LicenseRepository with BaseRepository {
  final BaseApiServices networkApiService;

  LicenseRepository({
    required this.networkApiService,
  });

  // * Get License List
  Future<LicenseListModel> getLicenseList() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.licensesList;

        final response = await networkApiService.getResponse(url);

        if (response == null) {
          return LicenseListModel.empty();
        }

        final licenseListModel = LicenseListModel.fromJson(response);

        return licenseListModel;
      },
    );
  }
}
