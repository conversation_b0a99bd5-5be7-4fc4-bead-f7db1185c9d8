import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/licenses/models/license_model.dart';
import 'package:xr_helper/xr_helper.dart';

class LicenseActionButtons extends StatelessWidget {
  final LicenseDetailPermissions permissions;
  final LicenseDetailData licenseData;
  final Function(String) onActionPressed;

  const LicenseActionButtons({
    super.key,
    required this.permissions,
    required this.licenseData,
    required this.onActionPressed,
  });

  @override
  Widget build(BuildContext context) {
    final availableActions = _getAvailableActions();

    if (availableActions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpaces.padding16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.radius12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.tr.availableActions,
                style: AppTextStyles.title.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              AppGaps.gap12,
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: availableActions.map((action) {
                  return _buildActionButton(context, action);
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<ActionButtonData> _getAvailableActions() {
    List<ActionButtonData> actions = [];

    // Client approval action - only show if license can be updated
    // Based on user notes: status update starts from client approval with mandatory attachment
    if (permissions.edit) {
      actions.add(ActionButtonData(
        key: 'client_approval',
        label: 'Client Approval',
        icon: Icons.approval,
        color: Colors.green,
      ));
    }

    return actions;
  }

  Widget _buildActionButton(BuildContext context, ActionButtonData action) {
    return Button(
      label: action.label,
      onPressed: () => onActionPressed(action.key),
      color: action.color,
      icon: action.icon,
      size: ButtonSize.small,
    );
  }
}

class ActionButtonData {
  final String key;
  final String label;
  final IconData icon;
  final Color color;

  ActionButtonData({
    required this.key,
    required this.label,
    required this.icon,
    required this.color,
  });
}
