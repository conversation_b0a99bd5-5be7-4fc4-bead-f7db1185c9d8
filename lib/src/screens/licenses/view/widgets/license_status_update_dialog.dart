import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/licenses/models/license_model.dart';
import 'package:opti_tickets/src/screens/licenses/providers/license_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class LicenseStatusUpdateDialog extends HookConsumerWidget {
  final LicenseDetailData licenseData;
  final String statusId;
  final bool isAttachmentRequired;

  const LicenseStatusUpdateDialog({
    super.key,
    required this.licenseData,
    required this.statusId,
    required this.isAttachmentRequired,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final reasonController = useTextEditingController();
    final selectedFile = useState<File?>(null);
    final isLoading = useState(false);

    return AlertDialog(
      title: Text(context.tr.updateLicenseStatus),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Reason field
            Text(
              context.tr.reason,
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            AppGaps.gap8,
            TextField(
              controller: reasonController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: context.tr.enterReason,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                ),
              ),
            ),
            
            if (isAttachmentRequired) ...[
              AppGaps.gap16,
              Text(
                '${context.tr.attachment} *',
                style: AppTextStyles.body.copyWith(
                  fontWeight: FontWeight.w500,
                  color: ColorManager.errorColor,
                ),
              ),
              AppGaps.gap8,
              
              // File selection button
              InkWell(
                onTap: () => _pickFile(selectedFile),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: selectedFile.value != null 
                          ? Colors.green 
                          : ColorManager.lightGrey,
                    ),
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        selectedFile.value != null 
                            ? Icons.check_circle 
                            : Icons.attach_file,
                        color: selectedFile.value != null 
                            ? Colors.green 
                            : ColorManager.darkGrey,
                      ),
                      AppGaps.gap8,
                      Expanded(
                        child: Text(
                          selectedFile.value != null
                              ? selectedFile.value!.path.split('/').last
                              : context.tr.selectFile,
                          style: AppTextStyles.body.copyWith(
                            color: selectedFile.value != null 
                                ? Colors.green 
                                : ColorManager.darkGrey,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading.value ? null : () => Navigator.pop(context),
          child: Text(context.tr.cancel),
        ),
        ElevatedButton(
          onPressed: isLoading.value
              ? null
              : () => _updateStatus(
                    context,
                    ref,
                    reasonController.text,
                    selectedFile.value?.path,
                    isLoading,
                  ),
          child: isLoading.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(context.tr.update),
        ),
      ],
    );
  }

  Future<void> _pickFile(ValueNotifier<File?> selectedFile) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? file = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (file != null) {
        selectedFile.value = File(file.path);
      }
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _updateStatus(
    BuildContext context,
    WidgetRef ref,
    String reason,
    String? attachmentPath,
    ValueNotifier<bool> isLoading,
  ) async {
    // Validate required fields
    if (isAttachmentRequired && attachmentPath == null) {
      context.showBarMessage(
        context.tr.attachmentRequired,
        isError: true,
      );
      return;
    }

    isLoading.value = true;

    try {
      final licenseController = ref.read(licenseControllerNotifierProvider);
      
      final success = await licenseController.updateLicenseStatus(
        lcId: licenseData.ipd.toString(),
        statusId: statusId,
        reason: reason,
        attachmentPath: attachmentPath,
      );

      if (success) {
        // Refresh the license details
        ref.invalidate(getLicenseDetailsFutureProvider(licenseData.ipd));
        
        if (context.mounted) {
          Navigator.pop(context);
          context.showBarMessage(
            context.tr.statusUpdatedSuccessfully,
            isError: false,
          );
        }
      } else {
        if (context.mounted) {
          context.showBarMessage(
            context.tr.failedToUpdateStatus,
            isError: true,
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        context.showBarMessage(
          context.tr.somethingWentWrong,
          isError: true,
        );
      }
    } finally {
      isLoading.value = false;
    }
  }
}
