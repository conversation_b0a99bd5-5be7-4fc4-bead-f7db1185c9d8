import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:open_filex/open_filex.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:opti_tickets/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/core/utils/format_number.dart';
import 'package:opti_tickets/src/screens/licenses/models/license_model.dart';
import 'package:opti_tickets/src/screens/licenses/providers/license_providers.dart';
import 'package:opti_tickets/src/screens/licenses/view/widgets/license_action_buttons.dart';
import 'package:opti_tickets/src/screens/licenses/view/widgets/license_status_update_dialog.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/widgets/loading/loading_widget.dart';

class LicenseDetailsScreen extends ConsumerStatefulWidget {
  final int licenseId;

  const LicenseDetailsScreen({
    super.key,
    required this.licenseId,
  });

  @override
  ConsumerState<LicenseDetailsScreen> createState() =>
      _LicenseDetailsScreenState();
}

class _LicenseDetailsScreenState extends ConsumerState<LicenseDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final licenseDetailsAsyncValue =
        ref.watch(getLicenseDetailsFutureProvider(widget.licenseId));
    final licenseController = ref.watch(licenseControllerNotifierProvider);

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      bottomNavigationBar: const BottomNavBarWidget(),
      appBar: BaseAppBar(
        title: context.tr.licenseDetails,
        actions: licenseDetailsAsyncValue.maybeWhen(
          data: (licenseDetail) {
            if (licenseDetail.success &&
                licenseDetail.can.print &&
                !licenseController.isLoading) {
              return IconButton(
                icon: const Icon(Icons.print),
                onPressed: () => _showPrintDialog(context, licenseDetail.dt),
              );
            }
            return null;
          },
          orElse: () => null,
        ),
      ),
      body: licenseDetailsAsyncValue.when(
        data: (licenseDetail) {
          if (licenseController.isLoading) {
            return Padding(
              padding: const EdgeInsets.all(AppSpaces.padding16),
              child: Skeletonizer(
                enabled: true,
                child: Column(
                  children: [
                    _buildSkeletonCard(),
                    AppGaps.gap16,
                    _buildSkeletonCard(),
                    AppGaps.gap16,
                    _buildSkeletonCard(),
                  ],
                ),
              ),
            );
          }
          if (!licenseDetail.success) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: ColorManager.errorColor,
                  ),
                  AppGaps.gap16,
                  Text(
                    context.tr.noDataFound,
                    style: AppTextStyles.body.copyWith(
                      color: ColorManager.errorColor,
                    ),
                  ),
                ],
              ),
            );
          }

          return DefaultTabController(
            length: 3,
            child: Builder(
              builder: (context) {
                final tabController = DefaultTabController.of(context);
                return AnimatedBuilder(
                  animation: tabController!,
                  builder: (context, _) {
                    return SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header Card
                          Padding(
                            padding: const EdgeInsets.all(AppSpaces.padding16),
                            child: _buildHeaderCard(context, licenseDetail.dt),
                          ),

                          // Client Information Card
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: AppSpaces.padding16),
                            child: _buildClientCard(
                                context, licenseDetail.dt.client),
                          ),

                          // Action Buttons
                          LicenseActionButtons(
                            permissions: licenseDetail.can,
                            licenseData: licenseDetail.dt,
                            onActionPressed: (action) => _handleActionPressed(
                                context, action, licenseDetail.dt),
                          ),

                          AppGaps.gap16,

                          // Tab Bar
                          Container(
                            margin: const EdgeInsets.symmetric(
                                horizontal: AppSpaces.padding16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.circular(AppRadius.radius12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: TabBar(
                              labelColor: ColorManager.primaryColor,
                              unselectedLabelColor: ColorManager.darkGrey,
                              indicatorColor: ColorManager.primaryColor,
                              dividerHeight: 0,
                              indicatorWeight: 3,
                              tabs: [
                                Tab(
                                  text: context.tr.servicesTab,
                                  icon: const Icon(Icons.build),
                                ),
                                Tab(
                                  text: context.tr.installmentsTab,
                                  icon: const Icon(Icons.payment),
                                ),
                                Tab(
                                  text: context.tr.historyTab,
                                  icon: const Icon(Icons.history),
                                ),
                              ],
                            ),
                          ),

                          if (tabController.index == 0) ...[
                            Padding(
                              padding:
                                  const EdgeInsets.all(AppSpaces.padding16),
                              child: _buildServicesCard(
                                context,
                                licenseDetail.dt.subs,
                              ),
                            ),
                          ] else if (tabController.index == 1) ...[
                            Padding(
                              padding:
                                  const EdgeInsets.all(AppSpaces.padding16),
                              child: _buildInstallmentsCard(
                                context,
                                licenseDetail.dt.installments,
                                licenseDetail.dt.discount,
                              ),
                            ),
                          ] else
                            Padding(
                              padding:
                                  const EdgeInsets.all(AppSpaces.padding16),
                              child: _buildHistoryCard(
                                context,
                                licenseDetail.dt.history,
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          );
        },
        loading: () => Padding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Skeletonizer(
            enabled: true,
            child: Column(
              children: [
                _buildSkeletonCard(),
                AppGaps.gap16,
                _buildSkeletonCard(),
                AppGaps.gap16,
                _buildSkeletonCard(),
              ],
            ),
          ),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: ColorManager.errorColor,
              ),
              AppGaps.gap16,
              Text(
                context.tr.somethingWentWrong,
                style: AppTextStyles.body.copyWith(
                  color: ColorManager.errorColor,
                ),
              ),
              AppGaps.gap16,
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(
                      getLicenseDetailsFutureProvider(widget.licenseId));
                },
                child: Text(context.tr.retry),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context, LicenseDetailData data) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    data.unicode,
                    style: AppTextStyles.title.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green, // License is active
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                  child: Text(
                    data.status.name,
                    style: AppTextStyles.whiteLabelMedium.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            AppGaps.gap12,
            Row(
              children: [
                const Icon(
                  CupertinoIcons.calendar,
                  size: 16,
                  color: ColorManager.primaryColor,
                ),
                AppGaps.gap8,
                Text(
                  '${context.tr.createdAt}: ${data.createdAt}',
                  style: AppTextStyles.body,
                ),
              ],
            ),
            AppGaps.gap8,
            Row(
              children: [
                const Icon(
                  Icons.person,
                  size: 16,
                  color: ColorManager.primaryColor,
                ),
                AppGaps.gap8,
                Text(
                  '${context.tr.createdBy}: ${data.createdBy}',
                  style: AppTextStyles.body,
                ),
              ],
            ),
            if (data.approvedAt.isNotEmpty) ...[
              AppGaps.gap8,
              Row(
                children: [
                  const Icon(
                    Icons.check_circle,
                    size: 16,
                    color: Colors.green,
                  ),
                  AppGaps.gap8,
                  Text(
                    '${context.tr.approvedAt}: ${data.approvedAt}',
                    style: AppTextStyles.body,
                  ),
                ],
              ),
            ],
            if (data.status.message.isNotEmpty) ...[
              AppGaps.gap8,
              Row(
                children: [
                  const Icon(
                    Icons.message,
                    size: 16,
                    color: ColorManager.primaryColor,
                  ),
                  AppGaps.gap8,
                  Expanded(
                    child: Text(
                      data.status.message,
                      style: AppTextStyles.body,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildClientCard(BuildContext context, LicenseClient client) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.clientInformation,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap12,
            Row(
              children: [
                const Icon(
                  Icons.business,
                  size: 16,
                  color: ColorManager.primaryColor,
                ),
                AppGaps.gap8,
                Expanded(
                  child: Text(
                    client.cName,
                    style: AppTextStyles.body.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            if (client.cPerson.isNotEmpty) ...[
              AppGaps.gap8,
              Row(
                children: [
                  const Icon(
                    Icons.person,
                    size: 16,
                    color: ColorManager.primaryColor,
                  ),
                  AppGaps.gap8,
                  Expanded(
                    child: Text(
                      client.cPerson,
                      style: AppTextStyles.body,
                    ),
                  ),
                ],
              ),
            ],
            if (client.cAddress.isNotEmpty) ...[
              AppGaps.gap8,
              Row(
                children: [
                  const Icon(
                    Icons.location_on,
                    size: 16,
                    color: ColorManager.primaryColor,
                  ),
                  AppGaps.gap8,
                  Expanded(
                    child: Text(
                      client.cAddress,
                      style: AppTextStyles.body,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleActionPressed(
    BuildContext context,
    String action,
    LicenseDetailData licenseData,
  ) {
    switch (action) {
      case 'client_approval':
        _showStatusUpdateDialog(context, licenseData, '9', true);
        break;
    }
  }

  Widget _buildServicesCard(
      BuildContext context, List<LicenseService> services) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.servicesTab,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap12,
            ...services.map((service) => _buildServiceItem(context, service)),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceItem(BuildContext context, LicenseService service) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFf8f9fa),
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        border: Border.all(color: ColorManager.lightGrey.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            service.serviceName,
            style: AppTextStyles.body.copyWith(
              fontWeight: FontWeight.w600,
              color: ColorManager.primaryColor,
            ),
          ),
          if (service.serviceHasLimitUser) ...[
            AppGaps.gap8,
            Text(
              '${context.tr.userLimit}: ${service.limitUser}',
              style: AppTextStyles.body.copyWith(fontSize: 13),
            ),
          ],
          if (service.subservices.isNotEmpty) ...[
            AppGaps.gap8,
            ...service.subservices.map((subservice) => Padding(
                  padding: const EdgeInsets.only(left: 16, bottom: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 4,
                        decoration: const BoxDecoration(
                          color: ColorManager.primaryColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      AppGaps.gap8,
                      Expanded(
                        child: Text(
                          subservice.subserviceName,
                          style: AppTextStyles.body.copyWith(fontSize: 14),
                        ),
                      ),
                      Text(
                        formatNumber(subservice.subserviceCost),
                        style: AppTextStyles.body.copyWith(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ],
      ),
    );
  }

  Widget _buildInstallmentsCard(BuildContext context,
      List<LicenseInstallment> installments, String discount) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.installmentsTab,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (discount.isNotEmpty && discount != '0') ...[
              AppGaps.gap12,
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.discount, color: Colors.green),
                    AppGaps.gap8,
                    Text(
                      '${context.tr.discount}: ${formatNumber(double.tryParse(discount) ?? 0.0)}',
                      style: AppTextStyles.body.copyWith(
                        fontWeight: FontWeight.w500,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            AppGaps.gap12,
            ...installments.map(
                (installment) => _buildInstallmentItem(context, installment)),
          ],
        ),
      ),
    );
  }

  Widget _buildInstallmentItem(
      BuildContext context, LicenseInstallment installment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFf8f9fa),
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        border: Border.all(color: ColorManager.lightGrey.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: ColorManager.primaryColor,
              borderRadius: BorderRadius.circular(AppRadius.radius8),
            ),
            child: Center(
              child: Text(
                installment.ccpayInstallNo,
                style: AppTextStyles.whiteLabelMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          AppGaps.gap12,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${context.tr.dueDate}: ${installment.ccpayInstallDue}',
                  style: AppTextStyles.body.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                AppGaps.gap4,
                Text(
                  formatNumber(
                      double.tryParse(installment.ccpayInstallAmount) ?? 0.0),
                  style: AppTextStyles.body.copyWith(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ColorManager.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryCard(BuildContext context, List<LicenseHistory> history) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.statusHistory,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap12,
            ...history.map((item) => _buildHistoryItem(context, item)),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryItem(BuildContext context, LicenseHistory item) {
    Color statusColor = _getHistoryColor(item.cchColor);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.historyDetail,
            style: AppTextStyles.body.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          AppGaps.gap4,
          Row(
            children: [
              Text(
                item.createdBy,
                style: AppTextStyles.body.copyWith(
                  fontSize: 12,
                  color: ColorManager.darkGrey,
                ),
              ),
              const Spacer(),
              Text(
                item.createdAt,
                style: AppTextStyles.body.copyWith(
                  fontSize: 12,
                  color: ColorManager.darkGrey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showStatusUpdateDialog(
    BuildContext context,
    LicenseDetailData licenseData,
    String statusId,
    bool isAttachmentRequired,
  ) {
    showDialog(
      context: context,
      builder: (context) => LicenseStatusUpdateDialog(
        licenseData: licenseData,
        statusId: statusId,
        isAttachmentRequired: isAttachmentRequired,
      ),
    );
  }

  void _showPrintDialog(BuildContext context, LicenseDetailData licenseData) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr.selectPrintLanguage),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Button(
              label: context.tr.arabicPrint,
              onPressed: () {
                Navigator.pop(context);
                _handlePrint(context, licenseData, 'ar');
              },
              color: ColorManager.primaryColor,
            ),
            AppGaps.gap8,
            Button(
              label: context.tr.englishPrint,
              onPressed: () {
                Navigator.pop(context);
                _handlePrint(context, licenseData, 'en');
              },
              color: ColorManager.orange,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handlePrint(
    BuildContext context,
    LicenseDetailData licenseData,
    String language,
  ) async {
    try {
      final licenseController = ref.read(licenseControllerNotifierProvider);
      final pdfBytes = await licenseController.printLicense(
        licenseId: licenseData.ipd,
        language: language,
      );

      if (pdfBytes != null) {
        // Save PDF to temporary directory and open it
        await _savePdfAndOpen(pdfBytes, licenseData.ipd, language);
      } else {
        if (context.mounted) {
          context.showBarMessage(
            context.tr.failedToGeneratePdf,
            isError: true,
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context);
        context.showBarMessage(
          context.tr.failedToGeneratePdf,
          isError: true,
        );
      }
    }
  }

  Future<void> _savePdfAndOpen(
      Uint8List pdfBytes, int licenseId, String language) async {
    try {
      // Get app documents directory
      final directory = Directory.systemTemp;
      final fileName = 'license_${licenseId}_$language.pdf';
      final file = File('${directory.path}/$fileName');

      // Write PDF bytes to file
      await file.writeAsBytes(pdfBytes);

      // Open the PDF file
      final result = await OpenFilex.open(file.path);

      if (result.type != ResultType.done) {
        throw Exception('Failed to open PDF: ${result.message}');
      }
    } catch (e) {
      throw Exception('Failed to save and open PDF: $e');
    }
  }

  Color _getHistoryColor(String color) {
    switch (color.toLowerCase()) {
      case 'success':
        return Colors.green;
      case 'warning':
        return Colors.orange;
      case 'danger':
        return Colors.red;
      case 'info':
        return Colors.blue;
      default:
        return ColorManager.primaryColor;
    }
  }

  Widget _buildSkeletonCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: const Padding(
        padding: EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          children: [
            Bone.text(words: 3),
            AppGaps.gap8,
            Bone.text(words: 5),
            AppGaps.gap8,
            Bone.text(words: 4),
          ],
        ),
      ),
    );
  }
}
