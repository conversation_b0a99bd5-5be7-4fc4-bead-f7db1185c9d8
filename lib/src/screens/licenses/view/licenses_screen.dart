import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/licenses/models/license_model.dart';
import 'package:opti_tickets/src/screens/licenses/providers/license_providers.dart';
import 'package:opti_tickets/src/screens/licenses/view/widgets/license_card_widget.dart';
import 'package:opti_tickets/src/screens/quotations/view/add_quotation_steps_screen.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

class LicensesScreen extends HookConsumerWidget {
  const LicensesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final licenseAsyncValue = ref.watch(getLicenseListFutureProvider);
    final searchController = useTextEditingController();
    final searchQuery = useState('');

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      bottomNavigationBar: const BottomNavBarWidget(),
      floatingActionButton: licenseAsyncValue.when(
        data: (licenseModel) {
          if (licenseModel.can.add) {
            return FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddQuotationStepsScreen(),
                  ),
                ).then((_) {
                  // Refresh the list when returning from add screen
                  ref.invalidate(getLicenseListFutureProvider);
                });
              },
              backgroundColor: Colors.orange,
              child: const Icon(Icons.add),
            );
          }
          return const SizedBox.shrink();
        },
        loading: () => const SizedBox.shrink(),
        error: (_, __) => const SizedBox.shrink(),
      ),
      appBar: AppBar(
        title: Text(context.tr.licenses),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.all(AppSpaces.padding16),
            child: TextField(
              controller: searchController,
              onChanged: (value) {
                searchQuery.value = value;
              },
              decoration: InputDecoration(
                hintText: context.tr.searchByCodeOrTitle,
                prefixIcon: const Icon(Icons.search),
                suffixIcon: searchQuery.value.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          searchController.clear();
                          searchQuery.value = '';
                        },
                      )
                    : null,
                filled: true,
                fillColor: Colors.white,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppRadius.radius12),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
        ),
      ),
      body: licenseAsyncValue.when(
        data: (licenseModel) {
          // Filter licenses based on search query
          final filteredLicenses = licenseModel.licenses.where((license) {
            if (searchQuery.value.isEmpty) return true;

            final query = searchQuery.value.toLowerCase();
            return license.unicode.toLowerCase().contains(query) ||
                license.client.toLowerCase().contains(query);
          }).toList();

          if (filteredLicenses.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    searchQuery.value.isNotEmpty
                        ? Icons.search_off
                        : Icons.description_outlined,
                    size: 64,
                    color: ColorManager.lightGrey,
                  ),
                  AppGaps.gap16,
                  Text(
                    searchQuery.value.isNotEmpty
                        ? context.tr.noDataFound
                        : context.tr.noDataFound,
                    style: AppTextStyles.body.copyWith(
                      color: ColorManager.lightGrey,
                    ),
                  ),
                ],
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(AppSpaces.padding16),
            child: ListView.separated(
              itemCount: filteredLicenses.length,
              separatorBuilder: (context, index) => AppGaps.gap12,
              itemBuilder: (context, index) {
                final license = filteredLicenses[index];
                return LicenseCardWidget(license: license);
              },
            ),
          );
        },
        loading: () => Padding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Skeletonizer(
            enabled: true,
            child: ListView.separated(
              itemCount: 5,
              separatorBuilder: (context, index) => AppGaps.gap12,
              itemBuilder: (context, index) {
                return const LicenseCardWidget(
                  license: License(
                    unicode: 'Loading...',
                    client: 'Loading client name...',
                    createat: '2024-01-01',
                    status: LicenseStatus(
                      status: 'Loading...',
                      color: 'info',
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: ColorManager.errorColor,
              ),
              AppGaps.gap16,
              Text(
                context.tr.somethingWentWrong,
                style: AppTextStyles.body.copyWith(
                  color: ColorManager.errorColor,
                ),
              ),
              AppGaps.gap8,
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(getLicenseListFutureProvider);
                },
                child: Text(context.tr.retry),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
