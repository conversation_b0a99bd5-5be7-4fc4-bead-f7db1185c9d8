import 'package:opti_tickets/src/screens/licenses/models/license_model.dart';
import 'package:opti_tickets/src/screens/licenses/repositories/license_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class LicenseController extends BaseVM {
  final LicenseRepository licenseRepo;

  LicenseController({
    required this.licenseRepo,
  });

  // * Get License List
  Future<LicenseListModel> getLicenseList() async {
    return await baseFunction(
      () async {
        return await licenseRepo.getLicenseList();
      },
    );
  }
}
