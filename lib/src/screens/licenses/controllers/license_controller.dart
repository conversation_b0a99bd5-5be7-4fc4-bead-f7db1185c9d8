import 'dart:typed_data';

import 'package:opti_tickets/src/screens/licenses/models/license_model.dart';
import 'package:opti_tickets/src/screens/licenses/repositories/license_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class LicenseController extends BaseVM {
  final LicenseRepository licenseRepo;

  LicenseController({
    required this.licenseRepo,
  });

  // * Get License List
  Future<LicenseListModel> getLicenseList() async {
    return await baseFunction(
      () async {
        return await licenseRepo.getLicenseList();
      },
    );
  }

  // * Get License Details
  Future<LicenseDetailModel> getLicenseDetails({
    required int licenseId,
  }) async {
    return await baseFunction(
      () async {
        return await licenseRepo.getLicenseDetails(
          licenseId: licenseId,
        );
      },
    );
  }

  // * Update License Status
  Future<bool> updateLicenseStatus({
    required String lcId,
    required String statusId,
    String reason = '',
    String? attachmentPath,
  }) async {
    return await baseFunction(
      () async {
        return await licenseRepo.updateLicenseStatus(
          lcId: lcId,
          statusId: statusId,
          reason: reason,
          attachmentPath: attachmentPath,
        );
      },
    );
  }

  // * Print License
  Future<Uint8List?> printLicense({
    required int licenseId,
    required String language,
  }) async {
    return await baseFunction(
      () async {
        return await licenseRepo.printLicense(
          licenseId: licenseId,
          language: language,
        );
      },
    );
  }
}
