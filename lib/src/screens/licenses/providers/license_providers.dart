import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/license_controller.dart';
import '../models/license_model.dart';
import '../repositories/license_repository.dart';

// * License Repo Provider ========================================
final licenseRepoProvider = Provider<LicenseRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return LicenseRepository(networkApiService: networkApiService);
});

// * License Controller Provider ========================================
final licenseControllerProvider = Provider<LicenseController>(
  (ref) {
    final licenseRepo = ref.watch(licenseRepoProvider);

    return LicenseController(
      licenseRepo: licenseRepo,
    );
  },
);

// controller noti
final licenseControllerNotifierProvider =
    ChangeNotifierProvider<LicenseController>(
  (ref) {
    final licenseRepo = ref.watch(licenseRepoProvider);

    return LicenseController(
      licenseRepo: licenseRepo,
    );
  },
);

// * Get License List Future Provider ========================================
final getLicenseListFutureProvider =
    FutureProvider.autoDispose<LicenseListModel>((ref) async {
  final licenseController = ref.watch(licenseControllerProvider);

  return await licenseController.getLicenseList();
});

// * Get License Details Future Provider ========================================
final getLicenseDetailsFutureProvider = FutureProvider.autoDispose
    .family<LicenseDetailModel, int>((ref, licenseId) async {
  final licenseController = ref.watch(licenseControllerProvider);

  return await licenseController.getLicenseDetails(licenseId: licenseId);
});
