import 'dart:typed_data';

import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:opti_tickets/src/screens/quotations/repositories/quotation_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class QuotationController extends BaseVM {
  final QuotationRepository quotationRepo;

  QuotationController({
    required this.quotationRepo,
  });

  // * Get Quotation List
  Future<QuotationListModel> getQuotationList() async {
    return await baseFunction(
      () async {
        return await quotationRepo.getQuotationList();
      },
    );
  }

  // * Add Quotation
  Future<Map<String, dynamic>?> addQuotation({
    required AddQuotationModel quotationData,
  }) async {
    return await baseFunction(
      () async {
        return await quotationRepo.addQuotation(
          quotationData: quotationData,
        );
      },
    );
  }

  // * Update Quotation
  Future<Map<String, dynamic>?> updateQuotation(
    UpdateQuotationModel quotationData,
  ) async {
    return await baseFunction(
      () async {
        return await quotationRepo.updateQuotation(
          quotationData: quotationData,
        );
      },
    );
  }

  // * Get Quotation Details
  Future<QuotationDetailModel> getQuotationDetails({
    required int quotationId,
  }) async {
    return await baseFunction(
      () async {
        return await quotationRepo.getQuotationDetails(
          quotationId: quotationId,
        );
      },
    );
  }

  // * Assign Quotation Price
  Future<bool> assignQuotationPrice({
    required QuotationPricingModel pricingModel,
  }) async {
    return await baseFunction(
      () async {
        return await quotationRepo.assignQuotationPrice(
          pricingModel: pricingModel,
        );
      },
    );
  }

  // * Update Quotation Status
  Future<bool> updateQuotationStatus({
    required String qcId,
    required String statusId,
    String reason = '',
    String? attachmentPath,
  }) async {
    return await baseFunction(
      () async {
        return await quotationRepo.updateQuotationStatus(
          qcId: qcId,
          statusId: statusId,
          reason: reason,
          attachmentPath: attachmentPath,
        );
      },
    );
  }

  // * Print Quotation
  Future<Uint8List?> printQuotation({
    required int quotationId,
    required String language,
  }) async {
    return await baseFunction(
      () async {
        return await quotationRepo.printQuotation(
          quotationId: quotationId,
          language: language,
        );
      },
    );
  }
}
