import 'dart:convert';
import 'dart:developer';
import 'dart:typed_data';

import 'package:http/http.dart' as http;
import 'package:opti_tickets/src/core/consts/network/api_endpoints.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:xr_helper/xr_helper.dart';

class QuotationRepository with BaseRepository {
  final BaseApiServices networkApiService;

  QuotationRepository({
    required this.networkApiService,
  });

  // * Get Quotation List
  Future<QuotationListModel> getQuotationList() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.quotationsList;

        final response = await networkApiService.getResponse(url);

        if (response == null) {
          return QuotationListModel.empty();
        }

        final quotationListModel = QuotationListModel.fromJson(response);

        return quotationListModel;
      },
    );
  }

  // * Add Quotation
  Future<Map<String, dynamic>?> addQuotation({
    required AddQuotationModel quotationData,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.quotationSave;

        final res = await http.post(Uri.parse(url),
            body: jsonEncode(
              quotationData.toJson(),
            ),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization':
                  "Bearer ${GetStorageService.getData(key: LocalKeys.token)}",
            });

        log('Add quotation response: ${res.body} status: ${res.statusCode}');

        if (res.statusCode == 200) {
          final responseData = jsonDecode(res.body);
          return responseData;
        }

        return null;
      },
    );
  }

  // * Update Quotation
  Future<Map<String, dynamic>?> updateQuotation({
    required UpdateQuotationModel quotationData,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.quotationUpdate;

        final res = await http.post(Uri.parse(url),
            body: jsonEncode(
              quotationData.toJson(),
            ),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization':
                  "Bearer ${GetStorageService.getData(key: LocalKeys.token)}",
            });

        log('Update quotation response: ${res.body} status: ${res.statusCode}');

        if (res.statusCode == 200) {
          final responseData = jsonDecode(res.body);
          return responseData;
        }

        return null;
      },
    );
  }

  // * Get Quotation Details
  Future<QuotationDetailModel> getQuotationDetails({
    required int quotationId,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.quotationDetails;

        final response = await networkApiService.postResponse(url, body: {
          'q_id': quotationId,
        });

        if (response == null) {
          return QuotationDetailModel.empty();
        }

        final quotationDetailModel = QuotationDetailModel.fromJson(response);

        return quotationDetailModel;
      },
    );
  }

  // * Assign Quotation Price
  Future<bool> assignQuotationPrice({
    required QuotationPricingModel pricingModel,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.quotationAssignPrice;

        final res = await http.post(
          Uri.parse(url),
          body: jsonEncode(pricingModel.toJson()),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization':
                "Bearer ${GetStorageService.getData(key: LocalKeys.token)}",
          },
        );
        log('Assign Price Response: ${res.body}');

        // final res = await networkApiService.postResponse(
        //   url,
        //   body: pricingModel.toJson(),
        //
        // );

        if (res.statusCode == 200 || res.statusCode == 201) {
          return true;
        } else {
          throw Exception('Failed to assign price: ${res.body}');
        }
      },
    );
  }

  // * Update Quotation Status
  Future<bool> updateQuotationStatus({
    required String qcId,
    required String statusId,
    String reason = '',
    String? attachmentPath,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.quotationStatusUpdate;

        final body = {
          'qc_id': qcId,
          'status_id': statusId,
          'reason': reason,
          'attachment': '',
        };

        final filePaths =
            attachmentPath != null ? [attachmentPath] : <String>[];

        final res = await networkApiService.postResponse(
          url,
          body: body,
          fieldName: 'attachment',
          filePaths: filePaths,
        );

        log('Status Update Response: $res');
        return true;
      },
    );
  }

  // * Print Quotation
  Future<Uint8List?> printQuotation({
    required int quotationId,
    required String language,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.quotationPrint;

        final body = {
          'q_id': quotationId.toString(),
          'q_lang': language,
          'q_action': 'd', // Use 'd' for download as suggested
        };

        final res = await http.post(
          Uri.parse(url),
          body: jsonEncode(body),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/pdf',
            'Authorization':
                "Bearer ${GetStorageService.getData(key: LocalKeys.token)}",
          },
        );

        log('Print Quotation Response Status: ${res.statusCode}');

        if (res.statusCode == 200) {
          // Return the PDF bytes
          return res.bodyBytes;
        } else {
          log('Print Quotation Error: ${res.body}');
          return null;
        }
      },
    );
  }
}
