import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:open_filex/open_filex.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:opti_tickets/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/core/utils/format_number.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:opti_tickets/src/screens/quotations/providers/quotation_providers.dart';
import 'package:opti_tickets/src/screens/quotations/view/quotation_pricing_screen.dart';
import 'package:opti_tickets/src/screens/quotations/view/widgets/status_update_dialog.dart';
import 'package:opti_tickets/src/screens/quotations/view/widgets/quotation_action_buttons.dart';
import 'package:opti_tickets/src/screens/quotations/view/widgets/quotation_calculation_footer.dart';
import 'package:opti_tickets/src/screens/quotations/view/edit_quotation_steps_screen.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/widgets/loading/loading_widget.dart';

//? Make as tabs
//? First is Basic Information (Client Name - Created Date - Permissions)
//?

class QuotationDetailsScreen extends ConsumerStatefulWidget {
  final int quotationId;

  const QuotationDetailsScreen({
    super.key,
    required this.quotationId,
  });

  @override
  ConsumerState<QuotationDetailsScreen> createState() =>
      _QuotationDetailsScreenState();
}

class _QuotationDetailsScreenState extends ConsumerState<QuotationDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final quotationDetailsAsyncValue =
        ref.watch(getQuotationDetailsFutureProvider(widget.quotationId));
    final quotationController = ref.watch(quotationControllerNotifierProvider);
    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      bottomNavigationBar: const BottomNavBarWidget(),
      appBar: BaseAppBar(
        title: context.tr.quotationDetails,
        actions: quotationDetailsAsyncValue.maybeWhen(
          data: (quotationDetail) {
            if (quotationDetail.success &&
                quotationDetail.can.print &&
                !quotationController.isLoading) {
              return IconButton(
                icon: const Icon(Icons.print),
                onPressed: () => _showPrintDialog(context, quotationDetail.dt),
              );
            }
            return null;
          },
          orElse: () => null,
        ),
      ),
      body: quotationDetailsAsyncValue.when(
        data: (quotationDetail) {
          if (quotationController.isLoading) {
            return Padding(
              padding: const EdgeInsets.all(AppSpaces.padding16),
              child: Skeletonizer(
                enabled: true,
                child: Column(
                  children: [
                    _buildSkeletonCard(),
                    AppGaps.gap16,
                    _buildSkeletonCard(),
                    AppGaps.gap16,
                    _buildSkeletonCard(),
                  ],
                ),
              ),
            );
          }
          if (!quotationDetail.success) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: ColorManager.errorColor,
                  ),
                  AppGaps.gap16,
                  Text(
                    context.tr.noDataFound,
                    style: AppTextStyles.body.copyWith(
                      color: ColorManager.errorColor,
                    ),
                  ),
                ],
              ),
            );
          }

          return DefaultTabController(
            length: 2,
            child: Builder(
              builder: (context) {
                final tabController = DefaultTabController.of(context);
                return AnimatedBuilder(
                  animation: tabController!,
                  builder: (context, _) {
                    return SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header Card
                          Padding(
                            padding: const EdgeInsets.all(AppSpaces.padding16),
                            child:
                                _buildHeaderCard(context, quotationDetail.dt),
                          ),

                          // Client Information Card
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: AppSpaces.padding16),
                            child: _buildClientCard(
                                context, quotationDetail.dt.client),
                          ),

                          // Action Buttons
                          QuotationActionButtons(
                            permissions: quotationDetail.can,
                            quotationData: quotationDetail.dt,
                            onActionPressed: (action) => _handleActionPressed(
                                context, action, quotationDetail.dt),
                          ),

                          AppGaps.gap16,

                          // Tab Bar
                          Container(
                            margin: const EdgeInsets.symmetric(
                                horizontal: AppSpaces.padding16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.circular(AppRadius.radius12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: TabBar(
                              labelColor: ColorManager.primaryColor,
                              unselectedLabelColor: ColorManager.darkGrey,
                              indicatorColor: ColorManager.primaryColor,
                              dividerHeight: 0,
                              indicatorWeight: 3,
                              tabs: [
                                Tab(
                                  text: context.tr.servicesTab,
                                  icon: const Icon(Icons.build),
                                ),
                                Tab(
                                  text: context.tr.historyTab,
                                  icon: const Icon(Icons.history),
                                ),
                              ],
                            ),
                          ),

                          if (tabController.index == 0) ...[
                            Padding(
                              padding:
                                  const EdgeInsets.all(AppSpaces.padding16),
                              child: _buildServicesCard(
                                context,
                                quotationDetail.dt.subs,
                                quotationDetail: quotationDetail.dt,
                              ),
                            ),
                          ] else
                            Padding(
                              padding:
                                  const EdgeInsets.all(AppSpaces.padding16),
                              child: _buildHistoryCard(
                                context,
                                quotationDetail.dt.history,
                              ),
                            ),

                          QuotationCalculationFooter(
                            quotationData: quotationDetail.dt,
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          );
        },
        loading: () => Padding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Skeletonizer(
            enabled: true,
            child: Column(
              children: [
                _buildSkeletonCard(),
                AppGaps.gap16,
                _buildSkeletonCard(),
                AppGaps.gap16,
                _buildSkeletonCard(),
              ],
            ),
          ),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: ColorManager.errorColor,
              ),
              AppGaps.gap16,
              Text(
                context.tr.somethingWentWrong,
                style: AppTextStyles.body.copyWith(
                  color: ColorManager.errorColor,
                ),
              ),
              AppGaps.gap16,
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(
                      getQuotationDetailsFutureProvider(widget.quotationId));
                },
                child: Text(context.tr.retry),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context, QuotationDetailData data) {
    Color _geColor(String color) {
      switch (color.toLowerCase()) {
        case 'success':
          return Colors.green;
        case 'warning':
          return Colors.orange;
        case 'danger':
          return Colors.red;
        case 'info':
          return Colors.blue;
        default:
          return ColorManager.primaryColor;
      }
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    data.unicode,
                    style: AppTextStyles.title.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _geColor(data.status.color),
                    // ColorManager.primaryColor,
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                  child: Text(
                    data.status.name,
                    style: AppTextStyles.whiteLabelMedium.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            AppGaps.gap12,
            Row(
              children: [
                const Icon(
                  CupertinoIcons.calendar,
                  size: 16,
                  color: ColorManager.primaryColor,
                ),
                AppGaps.gap8,
                Text(
                  '${context.tr.createdAt}: ${data.createdAt}',
                  style: AppTextStyles.body,
                ),
              ],
            ),
            AppGaps.gap8,
            Row(
              children: [
                const Icon(
                  Icons.person,
                  size: 16,
                  color: ColorManager.primaryColor,
                ),
                AppGaps.gap8,
                Text(
                  '${context.tr.createdBy}: ${data.createdBy}',
                  style: AppTextStyles.body,
                ),
              ],
            ),
            if (data.status.message.isNotEmpty) ...[
              AppGaps.gap8,
              Row(
                children: [
                  const Icon(
                    Icons.message,
                    size: 16,
                    color: ColorManager.primaryColor,
                  ),
                  AppGaps.gap8,
                  Expanded(
                    child: Text(
                      data.status.message,
                      style: AppTextStyles.body,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildClientCard(BuildContext context, QuotationClient client) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.clientInformation,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap12,
            Row(
              children: [
                const Icon(
                  Icons.business,
                  size: 16,
                  color: ColorManager.primaryColor,
                ),
                AppGaps.gap8,
                Expanded(
                  child: Text(
                    client.cName,
                    style: AppTextStyles.body.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleActionPressed(
    BuildContext context,
    String action,
    QuotationDetailData quotationData,
  ) {
    switch (action) {
      case 'edit':
        _navigateToEditQuotation(context, quotationData);
        break;
      case 'price':
        QuotationPricingScreen(
          quotationData: quotationData,
        ).navigate;
        break;
      case 'client_approval':
        _showStatusUpdateDialog(context, quotationData, 3, false);
        break;
      case 'client_note':
        _showStatusUpdateDialog(context, quotationData, 6, true);
        break;
      case 'issue_license':
        _showStatusUpdateDialog(context, quotationData, 8, false);
        break;
    }
  }

  void _navigateToEditQuotation(
    BuildContext context,
    QuotationDetailData quotationData,
  ) {
    // Navigate to AddQuotationStepsScreen with edit mode
    // This will skip the first step (client selection) and start from step 1 (product selection)
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditQuotationStepsScreen(
          quotationData: quotationData,
        ),
      ),
    );
  }

  Widget _buildServicesCard(
      BuildContext context, List<QuotationService> services,
      {required QuotationDetailData quotationDetail}) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.servicesTab,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap12,
            ...services.map((service) =>
                _buildServiceItem(context, service, quotationDetail)),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceItem(BuildContext context, QuotationService service,
      QuotationDetailData quotationDetail) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFf8f9fa),
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        border: Border.all(color: ColorManager.lightGrey.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            service.serviceName,
            style: AppTextStyles.body.copyWith(
              fontWeight: FontWeight.w600,
              color: ColorManager.primaryColor,
            ),
          ),
          if (service.serviceHasLimitUser) ...[
            AppGaps.gap8,
            Text(
              '${context.tr.userLimit}: ${service.limitUser}',
              style: AppTextStyles.body.copyWith(fontSize: 13),
            ),
          ],
          // if (service.vat > 0 && quotationDetail.status.canShowPrice) ...[
          //   AppGaps.gap4,
          //   Text(
          //     '${context.tr.vat}: ${service.vat.toInt()}%',
          //     style: AppTextStyles.body.copyWith(fontSize: 13),
          //   ),
          // ],
          if (service.subservices.isNotEmpty) ...[
            AppGaps.gap8,
            ...service.subservices.map((subservice) => Padding(
                  padding: const EdgeInsets.only(left: 16, bottom: 4),
                  child: Row(
                    children: [
                      Container(
                        width: 4,
                        height: 4,
                        decoration: const BoxDecoration(
                          color: ColorManager.primaryColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      AppGaps.gap8,
                      Expanded(
                        child: Text(
                          subservice.subserviceName,
                          style: AppTextStyles.body.copyWith(fontSize: 14),
                        ),
                      ),
                      if (quotationDetail.status.canShowPrice)
                        Text(
                          formatNumber(
                              double.tryParse(subservice.subserviceCost) ??
                                  0.0),
                          style: AppTextStyles.body.copyWith(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                )),
          ],
        ],
      ),
    );
  }

  Widget _buildHistoryCard(
      BuildContext context, List<QuotationHistory> history) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.statusHistory,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            AppGaps.gap12,
            ...history.map((item) => _buildHistoryItem(context, item)),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryItem(BuildContext context, QuotationHistory item) {
    Color statusColor = _getHistoryColor(item.cchColor);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.radius8),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.historyDetail,
            style: AppTextStyles.body.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          AppGaps.gap4,
          Row(
            children: [
              Text(
                item.createdBy,
                style: AppTextStyles.body.copyWith(
                  fontSize: 12,
                  color: ColorManager.darkGrey,
                ),
              ),
              const Spacer(),
              Text(
                item.createdAt,
                style: AppTextStyles.body.copyWith(
                  fontSize: 12,
                  color: ColorManager.darkGrey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showStatusUpdateDialog(
    BuildContext context,
    QuotationDetailData quotationData,
    int statusId,
    bool isNoteRequired,
  ) {
    showDialog(
      context: context,
      builder: (context) => StatusUpdateDialog(
        quotationData: quotationData,
        statusId: statusId,
        isNoteRequired: isNoteRequired,
      ),
    );
  }

  void _showPrintDialog(
      BuildContext context, QuotationDetailData quotationData) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr.selectPrintLanguage),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Button(
              label: context.tr.arabicPrint,
              onPressed: () {
                Navigator.pop(context);
                _handlePrint(context, quotationData, 'ar');
              },
              color: ColorManager.primaryColor,
            ),
            AppGaps.gap8,
            Button(
              label: context.tr.englishPrint,
              onPressed: () {
                Navigator.pop(context);
                _handlePrint(context, quotationData, 'en');
              },
              color: ColorManager.orange,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handlePrint(
    BuildContext context,
    QuotationDetailData quotationData,
    String language,
  ) async {
    try {
      final quotationController = ref.read(quotationControllerNotifierProvider);
      final pdfBytes = await quotationController.printQuotation(
        quotationId: quotationData.ipd,
        language: language,
      );

      if (pdfBytes != null) {
        // Save PDF to temporary directory and open it
        await _savePdfAndOpen(pdfBytes, quotationData.ipd, language);
      } else {
        if (context.mounted) {
          context.showBarMessage(
            context.tr.failedToGeneratePdf,
            isError: true,
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.pop(context);
        context.showBarMessage(
          context.tr.failedToGeneratePdf,
          isError: true,
        );
      }
    }
  }

  Future<void> _savePdfAndOpen(
      Uint8List pdfBytes, int quotationId, String language) async {
    try {
      // Get app documents directory
      final directory = Directory.systemTemp;
      final fileName = 'quotation_${quotationId}_$language.pdf';
      final file = File('${directory.path}/$fileName');

      // Write PDF bytes to file
      await file.writeAsBytes(pdfBytes);

      // Open the PDF file
      final result = await OpenFilex.open(file.path);

      if (result.type != ResultType.done) {
        throw Exception('Failed to open PDF: ${result.message}');
      }
    } catch (e) {
      throw Exception('Failed to save and open PDF: $e');
    }
  }

  Color _getHistoryColor(String color) {
    switch (color.toLowerCase()) {
      case 'success':
        return Colors.green;
      case 'warning':
        return Colors.orange;
      case 'danger':
        return Colors.red;
      case 'info':
        return Colors.blue;
      default:
        return ColorManager.primaryColor;
    }
  }

  Widget _buildSkeletonCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: const Padding(
        padding: EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          children: [
            Bone.text(words: 3),
            AppGaps.gap8,
            Bone.text(words: 5),
            AppGaps.gap8,
            Bone.text(words: 4),
          ],
        ),
      ),
    );
  }
}
