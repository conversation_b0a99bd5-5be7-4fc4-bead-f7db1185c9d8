import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../quotation_details_screen.dart';

class QuotationCardWidget extends StatelessWidget {
  final Quotation quotation;

  const QuotationCardWidget({
    super.key,
    required this.quotation,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        QuotationDetailsScreen(quotationId: quotation.ipd).navigate;
      },
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.radius12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with quotation code and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      quotation.unicode,
                      style: AppTextStyles.title.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(quotation.status.color),
                      borderRadius: BorderRadius.circular(AppRadius.radius8),
                    ),
                    child: Text(
                      quotation.status.status,
                      style: AppTextStyles.whiteLabelMedium.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              AppGaps.gap12,

              // Client name
              Row(
                children: [
                  const Icon(
                    Icons.business,
                    size: 16,
                    color: ColorManager.primaryColor,
                  ),
                  AppGaps.gap8,
                  Expanded(
                    child: Text(
                      quotation.client,
                      style: AppTextStyles.body.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              AppGaps.gap8,

              // Created date
              Row(
                children: [
                  const Icon(
                    CupertinoIcons.calendar,
                    size: 16,
                    color: ColorManager.primaryColor,
                  ),
                  AppGaps.gap8,
                  Text(
                    quotation.createat,
                    style: AppTextStyles.body.copyWith(
                      color: ColorManager.black,
                    ),
                  ),
                ],
              ),

              // Services
              if (quotation.subscriptions.isNotEmpty) ...[
                AppGaps.gap8,
                Divider(
                  color: ColorManager.darkGrey.withOpacity(.4),
                ),
                AppGaps.gap8,
                ...quotation.subscriptions.entries.map((entry) {
                  return Wrap(
                    children: entry.value.map((subscription) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 4, right: 8),
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFd9e6f0),
                                borderRadius:
                                    BorderRadius.circular(AppRadius.radius8),
                              ),
                              child: Text(
                                subscription.serviceName,
                                style: AppTextStyles.body
                                    .copyWith(color: const Color(0xFF00569e)),
                              ),
                            ),
                            if (subscription.subServiceName.isNotEmpty) ...[
                              AppGaps.gap4,
                              Text(
                                subscription.subServiceName,
                                style: AppTextStyles.body,
                              ),
                            ],
                          ],
                        ),
                      );
                    }).toList(),
                  );
                }),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String color) {
    switch (color.toLowerCase()) {
      case 'info':
        return Colors.blue;
      case 'success':
        return Colors.green;
      case 'warning':
        return Colors.orange;
      case 'danger':
        return Colors.red;
      default:
        return ColorManager.primaryColor;
    }
  }
}
