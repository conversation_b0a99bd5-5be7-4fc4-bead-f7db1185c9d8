import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:open_filex/open_filex.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:opti_tickets/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:opti_tickets/src/screens/quotations/providers/quotation_providers.dart';
import 'package:opti_tickets/src/screens/quotations/view/quotation_pricing_screen.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/loading/loading_widget.dart';

class StatusUpdateDialog extends HookConsumerWidget {
  final QuotationDetailData quotationData;
  final int statusId;
  final bool isNoteRequired;

  const StatusUpdateDialog({
    super.key,
    required this.quotationData,
    required this.statusId,
    required this.isNoteRequired,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final noteController = useTextEditingController();
    final selectedImage = useState<File?>(null);
    final isLoading = useState(false);
    final picker = ImagePicker();

    Future<void> pickImage() async {
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );
      if (image != null) {
        selectedImage.value = File(image.path);
      }
    }

    Future<void> updateStatus() async {
      if (isNoteRequired && noteController.text.trim().isEmpty) {
        context.showBarMessage(
          context.tr.noteRequired,
          isError: true,
        );
        return;
      }

      try {
        isLoading.value = true;
        final quotationController = ref.read(quotationControllerProvider);

        await quotationController.updateQuotationStatus(
          qcId: quotationData.ipd.toString(),
          statusId: statusId.toString(),
          reason: noteController.text.trim(),
          attachmentPath: selectedImage.value?.path,
        );

        if (context.mounted) {
          Navigator.pop(context);
          context.showBarMessage(context.tr.statusUpdatedSuccessfully);

          // Refresh the quotation details
          ref.invalidate(getQuotationDetailsFutureProvider(quotationData.ipd));
          ref.invalidate(getQuotationListFutureProvider);
        }
      } catch (e) {
        if (context.mounted) {
          context.showBarMessage(
            context.tr.failedToUpdateStatus,
            isError: true,
          );
        }
      } finally {
        isLoading.value = false;
      }
    }

    return AlertDialog(
      title: Text(context.tr.updateStatus),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isNoteRequired) ...[
              Text(
                '${context.tr.note}:',
                style: AppTextStyles.body.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              AppGaps.gap8,
              TextFormField(
                controller: noteController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: context.tr.addNote,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                ),
              ),
              AppGaps.gap16,
            ],
            Text(
              context.tr.attachmentOptional,
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            AppGaps.gap8,
            if (selectedImage.value != null) ...[
              Container(
                height: 100,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                  border: Border.all(color: ColorManager.lightGrey),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                  child: Image.file(
                    selectedImage.value!,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              AppGaps.gap8,
            ],
            Button(
              label: context.tr.addAttachment,
              onPressed: pickImage,
              color: ColorManager.orange,
              icon: const Icon(
                Icons.attach_file,
              ),
              isPrefixIcon: true,
            ).sized(height: 45),
          ],
        ),
      ),
      actions: [
        Row(
          children: [
            Expanded(
              child: Button(
                label: context.tr.updateStatus,
                loadingWidget: const LoadingWidget(),
                onPressed: isLoading.value ? null : updateStatus,
                color: ColorManager.primaryColor,
                isLoading: isLoading.value,
              ).sized(height: 45),
            ),
            TextButton(
              onPressed: isLoading.value ? null : () => Navigator.pop(context),
              child: Text(context.tr.cancel),
            ),
          ],
        ),
      ],
    );
  }
}
