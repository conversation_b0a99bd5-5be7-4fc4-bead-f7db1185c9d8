import 'dart:developer';

import 'package:equatable/equatable.dart';

class QuotationListModel extends Equatable {
  final bool success;
  final QuotationPermissions can;
  final List<Quotation> quotations;

  const QuotationListModel({
    this.success = false,
    this.can = const QuotationPermissions(),
    this.quotations = const [],
  });

  factory QuotationListModel.fromJson(Map<String, dynamic> json) {
    return QuotationListModel(
      success: json['success'] ?? false,
      can: QuotationPermissions.fromJson(json['can'] ?? {}),
      quotations: (json['dt'] as List<dynamic>?)
              ?.map((e) => Quotation.fromJson(e))
              .toList() ??
          [],
    );
  }

  factory QuotationListModel.empty() => const QuotationListModel();

  @override
  List<Object?> get props => [success, can, quotations];
}

class QuotationPermissions extends Equatable {
  final bool add;

  const QuotationPermissions({
    this.add = false,
  });

  factory QuotationPermissions.fromJson(Map<String, dynamic> json) {
    return QuotationPermissions(
      add: json['add'] ?? false,
    );
  }

  @override
  List<Object?> get props => [add];
}

class Quotation extends Equatable {
  final int ipd;
  final String unicode;
  final String createat;
  final String client;
  final Map<String, List<QuotationSubscription>> subscriptions;
  final QuotationStatus status;

  const Quotation({
    this.ipd = 0,
    this.unicode = '',
    this.createat = '',
    this.client = '',
    this.subscriptions = const {},
    this.status = const QuotationStatus(),
  });

  factory Quotation.fromJson(Map<String, dynamic> json) {
    Map<String, List<QuotationSubscription>> subscriptionsMap = {};

    if (json['subscriptions'] != null) {
      (json['subscriptions'] as Map<String, dynamic>).forEach((key, value) {
        subscriptionsMap[key] = (value as List<dynamic>)
            .map((e) => QuotationSubscription.fromJson(e))
            .toList();
      });
    }

    return Quotation(
      ipd: json['ipd'] ?? 0,
      unicode: json['unicode'] ?? '',
      createat: json['createat'] ?? '',
      client: json['client'] ?? '',
      subscriptions: subscriptionsMap,
      status: QuotationStatus.fromJson(json['status'] ?? {}),
    );
  }

  @override
  List<Object?> get props =>
      [ipd, unicode, createat, client, subscriptions, status];
}

class QuotationSubscription extends Equatable {
  final String serviceName;
  final String subServiceName;

  const QuotationSubscription({
    this.serviceName = '',
    this.subServiceName = '',
  });

  factory QuotationSubscription.fromJson(Map<String, dynamic> json) {
    return QuotationSubscription(
      serviceName: json['service_name'] ?? '',
      subServiceName: json['subservice_name'] ?? '',
    );
  }

  @override
  List<Object?> get props => [serviceName, subServiceName];
}

class QuotationStatus extends Equatable {
  final int id;
  final String status;
  final String color;
  final String? message;

  const QuotationStatus({
    this.id = 0,
    this.status = '',
    this.color = '',
    this.message,
  });

  factory QuotationStatus.fromJson(Map<String, dynamic> json) {
    return QuotationStatus(
      id: json['id'] ?? 0,
      status: json['status'] ?? '',
      color: json['color'] ?? '',
      message: json['message'],
    );
  }

  @override
  List<Object?> get props => [id, status, color, message];
}

class AddQuotationModel extends Equatable {
  final QuotationInfo qInfo;
  final List<QuotationDetail> qDetails;
  final QuotationTask task;

  const AddQuotationModel({
    this.qInfo = const QuotationInfo(),
    this.qDetails = const [],
    this.task = const QuotationTask(),
  });

  Map<String, dynamic> toJson() {
    return {
      'q_info': qInfo.toJson(),
      'q_details': qDetails.map((e) => e.toJson()).toList(),
      'task': task.toJson(),
    };
  }

  @override
  List<Object?> get props => [qInfo, qDetails, task];
}

class QuotationInfo extends Equatable {
  final String clientID;
  final String createdDate;
  final String validityDays;

  const QuotationInfo({
    this.clientID = '',
    this.createdDate = '',
    this.validityDays = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'clientID': clientID,
      'created_date': createdDate,
      'validity_days': validityDays,
    };
  }

  @override
  List<Object?> get props => [clientID, createdDate, validityDays];
}

class QuotationDetail extends Equatable {
  final String productId;
  final String subProductId;
  final int hasLimitUsers;

  const QuotationDetail({
    this.productId = '',
    this.subProductId = '',
    this.hasLimitUsers = 0,
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'subProductId': subProductId,
      'hasLimitUsers': hasLimitUsers,
    };
  }

  @override
  List<Object?> get props => [productId, subProductId, hasLimitUsers];
}

class QuotationTask extends Equatable {
  final String startDate;
  final String startTime;
  final String finishDate;
  final String finishTime;

  const QuotationTask({
    this.startDate = '',
    this.startTime = '',
    this.finishDate = '',
    this.finishTime = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate,
      'start_time': startTime,
      'finish_date': finishDate,
      'finish_time': finishTime,
    };
  }

  @override
  List<Object?> get props => [startDate, startTime, finishDate, finishTime];
}

// ============================================================================
// Quotation Details Models
// ============================================================================

class QuotationDetailModel extends Equatable {
  final bool success;
  final QuotationDetailPermissions can;
  final QuotationDetailData dt;

  const QuotationDetailModel({
    this.success = false,
    this.can = const QuotationDetailPermissions(),
    this.dt = const QuotationDetailData(),
  });

  factory QuotationDetailModel.fromJson(Map<String, dynamic> json) {
    return QuotationDetailModel(
      success: json['success'] ?? false,
      can: QuotationDetailPermissions.fromJson(json['can'] ?? {}),
      dt: QuotationDetailData.fromJson(json['dt'] ?? {}),
    );
  }

  factory QuotationDetailModel.empty() => const QuotationDetailModel();

  @override
  List<Object?> get props => [success, can, dt];
}

class QuotationDetailPermissions extends Equatable {
  final bool edit;
  final bool price;
  final bool print;
  final bool send;

  const QuotationDetailPermissions({
    this.edit = false,
    this.price = false,
    this.print = false,
    this.send = false,
  });

  factory QuotationDetailPermissions.fromJson(Map<String, dynamic> json) {
    return QuotationDetailPermissions(
      edit: json['edit'] ?? false,
      price: json['price'] ?? false,
      print: json['print'] ?? false,
      send: json['send'] ?? false,
    );
  }

  @override
  List<Object?> get props => [edit, price, print, send];
}

class QuotationDetailData extends Equatable {
  final int ipd;
  final String unicode;
  final String createat;
  final QuotationClient client;
  final List<QuotationService> subs;
  final String discount;
  final String payStartdate;
  final String payEvery;
  final String totalInstallments;
  final List<QuotationInstallment> installments;
  final QuotationDetailStatus status;
  final String createdAt;
  final String createdBy;
  final String approvedAt;
  final String approvedBy;
  final bool hasSignedFile;
  final List<QuotationHistory> history;

  const QuotationDetailData({
    this.ipd = 0,
    this.unicode = '',
    this.createat = '',
    this.client = const QuotationClient(),
    this.subs = const [],
    this.discount = '',
    this.payStartdate = '',
    this.payEvery = '',
    this.totalInstallments = '',
    this.installments = const [],
    this.status = const QuotationDetailStatus(),
    this.createdAt = '',
    this.createdBy = '',
    this.approvedAt = '',
    this.approvedBy = '',
    this.hasSignedFile = false,
    this.history = const [],
  });

  factory QuotationDetailData.fromJson(Map<String, dynamic> json) {
    return QuotationDetailData(
      ipd: json['ipd'] ?? 0,
      unicode: json['unicode'] ?? '',
      createat: json['createat'] ?? '',
      client: QuotationClient.fromJson(json['client'] ?? {}),
      subs: (json['subs'] as List<dynamic>?)
              ?.map((e) => QuotationService.fromJson(e))
              .toList() ??
          [],
      discount: json['discount'] ?? '',
      payStartdate: json['pay_startdate'] ?? '',
      payEvery: json['pay_every'] ?? '',
      totalInstallments: json['total_installments'] ?? '',
      installments: (json['installments'] as List<dynamic>?)
              ?.map((e) => QuotationInstallment.fromJson(e))
              .toList() ??
          [],
      status: QuotationDetailStatus.fromJson(json['status'] ?? {}),
      createdAt: json['createdAt'] ?? '',
      createdBy: json['createdBy'] ?? '',
      approvedAt: json['ApprovedAt'] ?? '',
      approvedBy: json['ApprovedBy'] ?? '',
      hasSignedFile: json['has_SignedFile'] ?? false,
      history: (json['history'] as List<dynamic>?)
              ?.map((e) => QuotationHistory.fromJson(e))
              .toList() ??
          [],
    );
  }

  @override
  List<Object?> get props => [
        ipd,
        unicode,
        createat,
        client,
        subs,
        discount,
        payStartdate,
        payEvery,
        totalInstallments,
        installments,
        status,
        createdAt,
        createdBy,
        approvedAt,
        approvedBy,
        hasSignedFile,
        history,
      ];
}

class QuotationClient extends Equatable {
  final int cid;
  final String cName;
  final String cPerson; //? valid for check only
  final String cAddress; //? valid for check only

  const QuotationClient({
    this.cid = 0,
    this.cName = '',
    this.cPerson = '',
    this.cAddress = '',
  });

  factory QuotationClient.fromJson(Map<String, dynamic> json) {
    return QuotationClient(
      cid: json['cid'] ?? 0,
      cName: json['c_name'] ?? '',
      cPerson: json['c_person'] ?? '',
      cAddress: json['c_address'] ?? '',
    );
  }

  @override
  List<Object?> get props => [cid, cName, cPerson, cAddress];
}

class QuotationService extends Equatable {
  final int serviceId;
  final String serviceName;
  final bool serviceHasLimitUser;
  final String limitUser;
  final double extraUserCost;
  final double vat;
  final List<QuotationSubService> subservices;

  const QuotationService({
    this.serviceId = 0,
    this.serviceName = '',
    this.serviceHasLimitUser = false,
    this.limitUser = '',
    this.extraUserCost = 0.0,
    this.vat = 0.0,
    this.subservices = const [],
  });

  factory QuotationService.fromJson(Map<String, dynamic> json) {
    return QuotationService(
      serviceId: json['service_id'] ?? 0,
      serviceName: json['service_name'] ?? '',
      serviceHasLimitUser: json['service_hasLimitUser'] ?? false,
      limitUser: json['limitUser'] ?? '',
      extraUserCost:
          num.parse(json['extraUserCost']?.toString() ?? '0').toDouble(),
      vat: (json['vat'] ?? 0).toDouble(),
      subservices: (json['subservices'] as List<dynamic>?)
              ?.map((e) => QuotationSubService.fromJson(e))
              .toList() ??
          [],
    );
  }

  @override
  List<Object?> get props => [
        serviceId,
        serviceName,
        serviceHasLimitUser,
        limitUser,
        extraUserCost,
        vat,
        subservices,
      ];
}

class QuotationSubService extends Equatable {
  final int subserviceId;
  final String subserviceName;
  final String subserviceCost;

  const QuotationSubService({
    this.subserviceId = 0,
    this.subserviceName = '',
    this.subserviceCost = '',
  });

  factory QuotationSubService.fromJson(Map<String, dynamic> json) {
    return QuotationSubService(
      subserviceId: json['subservice_id'] ?? 0,
      subserviceName: json['subservice_name'] ?? '',
      subserviceCost: json['subservice_cost']?.toString() ?? '',
    );
  }

  @override
  List<Object?> get props => [subserviceId, subserviceName, subserviceCost];
}

class QuotationInstallment extends Equatable {
  final String amount;
  final String date;

  const QuotationInstallment({
    this.amount = '',
    this.date = '',
  });

  factory QuotationInstallment.fromJson(Map<String, dynamic> json) {
    return QuotationInstallment(
      amount: json['amount'] ?? '',
      date: json['date'] ?? '',
    );
  }

  @override
  List<Object?> get props => [amount, date];
}

class QuotationDetailStatus extends Equatable {
  final int? id;
  final String name;
  final String color;
  final String message;
  final String by;

  const QuotationDetailStatus({
    this.id,
    this.name = '',
    this.message = '',
    this.by = '',
    this.color = '',
  });

  //! under process
  bool get canShowPrice {
    return id != 1;
  }

  // Status action checks
  bool get canSave {
    return id == 1;
  }

  bool get canApproveClient {
    return id == 2;
  }

  bool get canIssueLicense {
    return id == 3;
  }

  factory QuotationDetailStatus.fromJson(Map<String, dynamic> json) {
    log('asfasfsaf ${json}');

    return QuotationDetailStatus(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      message: json['message'] ?? '',
      by: json['by'] ?? '',
      color: json['color'] ?? '',
    );
  }

  @override
  List<Object?> get props => [name, message, by];
}

class QuotationHistory extends Equatable {
  final String historyDetail;
  final String cchColor;
  final String createdAt;
  final String createdBy;

  const QuotationHistory({
    this.historyDetail = '',
    this.cchColor = '',
    this.createdAt = '',
    this.createdBy = '',
  });

  factory QuotationHistory.fromJson(Map<String, dynamic> json) {
    return QuotationHistory(
      historyDetail: json['history_detail'] ?? '',
      cchColor: json['cch_color'] ?? '',
      createdAt: json['created_at'] ?? '',
      createdBy: json['created_by'] ?? '',
    );
  }

  @override
  List<Object?> get props => [historyDetail, cchColor, createdAt, createdBy];
}

// ============================================================================
// Quotation Pricing Models
// ============================================================================

class QuotationPricingModel extends Equatable {
  final String qcId;
  final List<QuotationPricingDetail> qcDetails;
  final QuotationPayment qcPayment;

  const QuotationPricingModel({
    this.qcId = '',
    this.qcDetails = const [],
    this.qcPayment = const QuotationPayment(),
  });

  Map<String, dynamic> toJson() {
    return {
      'qc_id': qcId,
      'qc_details': qcDetails.map((e) => e.toJson()).toList(),
      'qc_payment': qcPayment.toJson(),
    };
  }

  @override
  List<Object?> get props => [qcId, qcDetails, qcPayment];
}

class QuotationPricingDetail extends Equatable {
  final String productId;
  final String extraUserCost;
  final List<QuotationPricingSubProduct> subproducts;

  const QuotationPricingDetail({
    this.productId = '',
    this.extraUserCost = '0',
    this.subproducts = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'extra_user_cost': extraUserCost,
      'subproducts': subproducts.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [productId, extraUserCost, subproducts];
}

class QuotationPricingSubProduct extends Equatable {
  final String subProductId;
  final String cost;

  const QuotationPricingSubProduct({
    this.subProductId = '',
    this.cost = '0',
  });

  Map<String, dynamic> toJson() {
    return {
      'subProductId': int.tryParse(subProductId),
      'cost': cost,
    };
  }

  @override
  List<Object?> get props => [subProductId, cost];
}

class QuotationPayment extends Equatable {
  final String initialAmount;
  final String discount;
  final String payInstallments;
  final String payStartDate;
  final String payEvery;

  const QuotationPayment({
    this.initialAmount = '0',
    this.discount = '0',
    this.payInstallments = '1',
    this.payStartDate = '',
    this.payEvery = '1',
  });

  Map<String, dynamic> toJson() {
    return {
      'initial_amount': initialAmount,
      'discount': discount,
      'payInstallments': payInstallments,
      'payStartDate': payStartDate,
      'payEvery': payEvery,
    };
  }

  @override
  List<Object?> get props => [
        initialAmount,
        discount,
        payInstallments,
        payStartDate,
        payEvery,
      ];
}

// ============================================================================
// Update Quotation Models
// ============================================================================

class UpdateQuotationModel extends Equatable {
  final QuotationUpdateInfo qInfo;
  final List<QuotationDetail> qDetails;
  final QuotationTask task;

  const UpdateQuotationModel({
    this.qInfo = const QuotationUpdateInfo(),
    this.qDetails = const [],
    this.task = const QuotationTask(),
  });

  Map<String, dynamic> toJson() {
    return {
      'q_info': qInfo.toJson(),
      'q_details': qDetails.map((e) => e.toJson()).toList(),
      'task': task.toJson(),
    };
  }

  @override
  List<Object?> get props => [qInfo, qDetails, task];
}

class QuotationUpdateInfo extends Equatable {
  final String clientID;
  final String quotationId;
  final String createdDate;
  final String validityDays;

  const QuotationUpdateInfo({
    this.clientID = '',
    this.quotationId = '',
    this.createdDate = '',
    this.validityDays = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'clientID': clientID,
      'quotation_id': quotationId,
      'created_date': createdDate,
      'validity_days': validityDays,
    };
  }

  @override
  List<Object?> get props => [clientID, quotationId, createdDate, validityDays];
}
