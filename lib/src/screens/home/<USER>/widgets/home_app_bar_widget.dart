import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/generated/assets.gen.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/screens/auth/providers/auth_providers.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/theme/color_manager.dart';
import '../../../settings/integrated_settings_screen.dart';

class HomeAppBarWidget extends ConsumerWidget {
  final Employee employee;

  const HomeAppBarWidget({
    super.key,
    required this.employee,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        // User info section (left)
        Row(
          children: [
            // User avatar
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const IntegratedSettingsScreen(),
                  ),
                );
              },
              child: CircleAvatar(
                radius: 18.r,
                backgroundColor: ColorManager.white,
                child: employee.empPIC.isNotEmpty
                    ? ClipOval(
                        child: Image.network(
                          employee.empPIC,
                          width: 36.r,
                          height: 36.r,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.person,
                              size: 20.r,
                              color: ColorManager.primaryColor,
                            );
                          },
                        ),
                      )
                    : Icon(
                        Icons.person,
                        size: 20.r,
                        color: ColorManager.primaryColor,
                      ),
              ),
            ),
            AppGaps.gap8,
            // User name only (smaller)
            Text(
              employee.empName.isNotEmpty
                  ? employee.empName
                  : context.tr.welcomeBack,
              style: AppTextStyles.title.copyWith(
                fontFamily: GoogleFonts.cairo().fontFamily,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),

        // Logo in the middle
        Expanded(
          child: Center(
            child: Assets.images.ivLogo.image(
              width: 80.w,
              fit: BoxFit.cover,
            ),
          ),
        ),

        // Action buttons (right)
        Row(
          children: [
            // Support icon
            IconButton(
              onPressed: () {
                // TODO: Add support functionality
              },
              icon: const Icon(
                Icons.headset_mic,
                color: ColorManager.black,
                size: 20,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
            AppGaps.gap8,
            // Logout icon
            IconButton(
              onPressed: () => showDialog(
                context: context,
                builder: (_) => const LogoutDialog(),
              ),
              icon: const Icon(
                Icons.logout,
                color: ColorManager.black,
                size: 20,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ),
      ],
    );
  }
}

class LogoutDialog extends HookConsumerWidget {
  const LogoutDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider);

    return AlertDialog(
      surfaceTintColor: Colors.white,
      title: Text(
        context.tr.logout,
        style: AppTextStyles.title
            .copyWith(fontWeight: FontWeight.bold, fontSize: 22),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            context.tr.areYouSureYouWantToLogout,
            style: AppTextStyles.subTitle.copyWith(fontWeight: FontWeight.bold),
          ),
          AppGaps.gap24,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: () {
                  navService.back();
                },
                child: Text(
                  context.tr.cancel,
                  style: AppTextStyles.labelLarge,
                ),
              ),
              TextButton(
                onPressed: () {
                  authController.logout();
                },
                child: Text(
                  context.tr.confirm,
                  style: AppTextStyles.labelLarge.copyWith(
                    color: ColorManager.errorColor,
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
