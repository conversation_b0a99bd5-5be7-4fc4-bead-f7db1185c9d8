import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/providers/auth_providers.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_model.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeFuture = ref.watch(getHomeFutureProvider);

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      bottomNavigationBar: const BottomNavBarWidget(),
      body: homeFuture.when(
        data: (homeData) => _buildProfileContent(context, ref, homeData),
        loading: () => _buildLoadingContent(context),
        error: (error, stackTrace) => _buildErrorContent(context),
      ),
    );
  }

  Widget _buildProfileContent(
      BuildContext context, WidgetRef ref, HomeModel homeData) {
    final employee = homeData.employee;

    return CustomScrollView(
      slivers: [
        // Sliver App Bar with Profile Header
        SliverAppBar(
          expandedHeight: 280,
          pinned: true,
          backgroundColor: ColorManager.primaryColor,
          foregroundColor: Colors.white,
          flexibleSpace: FlexibleSpaceBar(
            title: Text(
              context.tr.settings,
              style: AppTextStyles.title.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            centerTitle: true,
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    ColorManager.primaryColor,
                    ColorManager.primaryColor.withOpacity(0.8),
                  ],
                ),
              ),
              child: SafeArea(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppGaps.gap32,
                    // Profile Picture
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white,
                          width: 4,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: ClipOval(
                        child: employee.empPIC.isNotEmpty
                            ? Image.network(
                                employee.empPIC,
                                width: 100,
                                height: 100,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return CircleAvatar(
                                    backgroundColor: Colors.white,
                                    child: Icon(
                                      Icons.person,
                                      size: 50,
                                      color: ColorManager.primaryColor,
                                    ),
                                  );
                                },
                              )
                            : CircleAvatar(
                                backgroundColor: Colors.white,
                                child: Icon(
                                  Icons.person,
                                  size: 50,
                                  color: ColorManager.primaryColor,
                                ),
                              ),
                      ),
                    ),
                    AppGaps.gap16,
                    // User Info Row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Column(
                          children: [
                            Text(
                              employee.empName,
                              style: AppTextStyles.subTitle.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            AppGaps.gap4,
                            Text(
                              employee.empJob,
                              style: AppTextStyles.body.copyWith(
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        // Profile Information Cards
        SliverPadding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          sliver: SliverList(
            delegate: SliverChildListDelegate([
              // Profile Information Cards
              _buildInfoCard(
                context,
                icon: Icons.person,
                title: context.tr.empName,
                value: employee.empName,
              ),

              AppGaps.gap16,

              _buildInfoCard(
                context,
                icon: Icons.work,
                title: context.tr.empJob,
                value: employee.empJob,
              ),
              AppGaps.gap16,

              _buildInfoCard(
                context,
                icon: Icons.access_time,
                title: context.tr.workTime,
                value:
                    '${employee.workTimings.start ?? 'N/A'} - ${employee.workTimings.end ?? 'N/A'}',
              ),

              AppGaps.gap32,

              // Logout Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _showLogoutDialog(context, ref),
                  icon: const Icon(Icons.logout),
                  label: Text(context.tr.logout),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorManager.errorColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                  ),
                ),
              ),

              AppGaps.gap32,
            ]),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: ColorManager.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppRadius.radius12),
              ),
              child: Icon(
                icon,
                color: ColorManager.primaryColor,
                size: 24,
              ),
            ),
            AppGaps.gap16,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.labelLarge.copyWith(
                      color: ColorManager.darkGrey,
                    ),
                  ),
                  AppGaps.gap4,
                  Text(
                    value,
                    style: AppTextStyles.subTitle.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingContent(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorContent(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: ColorManager.errorColor,
          ),
          AppGaps.gap16,
          Text(
            context.tr.somethingWentWrong,
            style: AppTextStyles.body.copyWith(
              color: ColorManager.errorColor,
            ),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr.logout),
        content: Text(context.tr.areYouSureYouWantToLogout ??
            'Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(authControllerProvider).logout();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorManager.errorColor,
            ),
            child: Text(
              context.tr.logout,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
