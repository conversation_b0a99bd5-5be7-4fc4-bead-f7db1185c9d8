import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/clients/models/client_model.dart';
import 'package:opti_tickets/src/screens/clients/providers/client_providers.dart';
import 'package:opti_tickets/src/screens/clients/view/add_client_screen.dart';
import 'package:opti_tickets/src/screens/clients/view/widgets/client_card_widget.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

class ClientsScreen extends ConsumerStatefulWidget {
  const ClientsScreen({super.key});

  @override
  ConsumerState<ClientsScreen> createState() => _ClientsScreenState();
}

class _ClientsScreenState extends ConsumerState<ClientsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final clientFuture = ref.watch(getClientListFutureProvider);

    final clientModel = clientFuture.when(
      data: (clientData) => clientData,
      loading: () => const ClientListModel(),
      error: (error, stackTrace) => const ClientListModel(),
    );

    final isLoading = clientFuture.isLoading;

    final activeClients =
        clientModel.clients.where((client) => client.isActive).toList();
    final leadClients =
        clientModel.clients.where((client) => client.isLead).toList();

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      bottomNavigationBar: const BottomNavBarWidget(),
      floatingActionButton: clientModel.can.add
          ? FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddClientScreen(),
                  ),
                ).then((_) {
                  // Refresh the list when returning from add screen
                  ref.invalidate(getClientListFutureProvider);
                });
              },
              backgroundColor: ColorManager.primaryColor,
              child: const Icon(Icons.add, color: Colors.white),
            )
          : null,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              backgroundColor: ColorManager.primaryColor,
              foregroundColor: Colors.white,
              pinned: true,
              floating: true,
              expandedHeight: 120,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  context.tr.clients,
                  style: AppTextStyles.title.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                centerTitle: true,
              ),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(50),
                child: Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: ColorManager.primaryColor,
                    unselectedLabelColor: ColorManager.darkGrey,
                    indicatorColor: ColorManager.primaryColor,
                    indicatorWeight: 3,
                    indicatorSize: TabBarIndicatorSize.tab,
                    dividerHeight: 0,
                    tabs: [
                      Tab(
                        text: context.tr.active,
                        icon: const Icon(Icons.business, size: 20),
                      ),
                      Tab(
                        text: context.tr.leads,
                        icon: const Icon(Icons.people, size: 20),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ];
        },
        body: Skeletonizer(
          enabled: isLoading,
          child: TabBarView(
            controller: _tabController,
            children: [
              // Active Clients Tab
              _buildClientList(
                clients: isLoading ? _getDemoClients() : activeClients,
                isLoading: isLoading,
                emptyMessage: context.tr.noDataFound,
              ),
              // Leads Tab
              _buildClientList(
                clients: isLoading ? _getDemoClients() : leadClients,
                isLoading: isLoading,
                emptyMessage: context.tr.noDataFound,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildClientList({
    required List<Client> clients,
    required bool isLoading,
    required String emptyMessage,
  }) {
    if (clients.isEmpty && !isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.business,
              size: 64,
              color: ColorManager.lightGrey,
            ),
            AppGaps.gap16,
            Text(
              emptyMessage,
              style: AppTextStyles.subTitle.copyWith(
                color: ColorManager.lightGrey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(getClientListFutureProvider);
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        itemCount: clients.length,
        itemBuilder: (context, index) {
          final client = clients[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: AppSpaces.padding16),
            child: ClientCardWidget(
              client: client,
            ),
          );
        },
      ),
    );
  }

  List<Client> _getDemoClients() {
    return List.generate(
      5,
      (index) => const Client(
        clientID: 1,
        clientname: 'Demo Client',
        clientstatus: 'active',
      ),
    );
  }
}
