import 'package:equatable/equatable.dart';

class ClientListModel extends Equatable {
  final ClientPermissions can;
  final List<Client> clients;

  const ClientListModel({
    this.can = const ClientPermissions(),
    this.clients = const [],
  });

  factory ClientListModel.fromJson(Map<String, dynamic> json) {
    return ClientListModel(
      can: ClientPermissions.fromJson(json['can'] ?? {}),
      clients: (json['dt'] as List<dynamic>?)
              ?.map((e) => Client.fromJson(e))
              .toList() ??
          [],
    );
  }

  factory ClientListModel.empty() => const ClientListModel();

  @override
  List<Object?> get props => [can, clients];
}

class ClientPermissions extends Equatable {
  final bool add;
  final bool edit;
  final bool suspend;

  const ClientPermissions({
    this.add = false,
    this.edit = false,
    this.suspend = false,
  });

  factory ClientPermissions.fromJson(Map<String, dynamic> json) {
    return ClientPermissions(
      add: json['add'] ?? false,
      edit: json['edit'] ?? false,
      suspend: json['suspend'] ?? false,
    );
  }

  @override
  List<Object?> get props => [add, edit, suspend];
}

class Client extends Equatable {
  final int clientID;
  final String clientname;
  final String clientstatus;
  final String clientLogo;
  // Additional fields to match QuotationClient
  final int cid;
  final String cName;
  final String cPerson;
  final String cAddress;

  const Client({
    this.clientID = 0,
    this.clientname = '',
    this.clientstatus = '',
    this.clientLogo = '',
    this.cid = 0,
    this.cName = '',
    this.cPerson = '',
    this.cAddress = '',
  });

  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      clientID: json['clientID'] ?? 0,
      clientname: json['clientname'] ?? '',
      clientstatus: json['clientstatus'] ?? '',
      clientLogo: json['clientLogo'] ?? '',
      cid: json['cid'] ?? json['clientID'] ?? 0,
      cName: json['c_name'] ?? json['clientname'] ?? '',
      cPerson: json['c_person'] ?? '',
      cAddress: json['c_address'] ?? '',
    );
  }

  bool get isActive => clientstatus == 'active';
  bool get isLead => clientstatus == 'leads';

  @override
  List<Object?> get props => [
        clientID,
        clientname,
        clientstatus,
        clientLogo,
        cid,
        cName,
        cPerson,
        cAddress,
      ];
}

class AddClientModel extends Equatable {
  final String clName;
  final String clCr;
  final String clResponsibleName;
  final String clResponsibleJob;
  final String clEmail;
  final String clMobile;
  final ClientTask task;

  const AddClientModel({
    this.clName = '',
    this.clCr = '',
    this.clResponsibleName = '',
    this.clResponsibleJob = '',
    this.clEmail = '',
    this.clMobile = '',
    this.task = const ClientTask(),
  });

  Map<String, dynamic> toJson() {
    return {
      'client': {
        'cl_name': clName,
        'cl_cr': clCr,
        'cl_responsible_name': clResponsibleName,
        'cl_responsible_job': clResponsibleJob,
        'cl_email': clEmail,
        'cl_mobile': clMobile,
      },
      'task': task.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        clName,
        clCr,
        clResponsibleName,
        clResponsibleJob,
        clEmail,
        clMobile,
        task,
      ];
}

class ClientTask extends Equatable {
  final String startDate;
  final String startTime;
  final String finishDate;
  final String finishTime;

  const ClientTask({
    this.startDate = '',
    this.startTime = '',
    this.finishDate = '',
    this.finishTime = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate,
      'start_time': startTime,
      'finish_date': finishDate,
      'finish_time': finishTime,
    };
  }

  @override
  List<Object?> get props => [startDate, startTime, finishDate, finishTime];
}
