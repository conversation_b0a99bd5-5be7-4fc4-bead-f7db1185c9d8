import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/clients/view/clients_screen.dart';
import 'package:opti_tickets/src/screens/licenses/view/licenses_screen.dart';
import 'package:opti_tickets/src/screens/quotations/view/quotations_screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared/widgets/navigations/bottom_nav_bar.widget.dart';

class ServicesScreen extends ConsumerWidget {
  const ServicesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      bottomNavigationBar: const BottomNavBarWidget(),
      appBar: BaseAppBar(
        title: context.tr.services,
        showBackButton: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العملاء Section
            _buildSectionTitle(context, context.tr.customers),
            AppGaps.gap16,
            _buildServiceGrid(context, [
              _ServiceItem(
                icon: Icons.people,
                title: context.tr.customersList,
                onTap: () {
                  const ClientsScreen().navigate;
                },
              ),
              _ServiceItem(
                icon: Icons.meeting_room,
                title: context.tr.customerMeetings,
                onTap: () {
                  // Navigate to meetings
                },
              ),
              _ServiceItem(
                icon: Icons.rate_review,
                title: context.tr.customerOpinions,
                onTap: () {
                  // Navigate to customer opinions
                },
              ),
            ]),

            AppGaps.gap32,

            // الاتفاقيات Section
            _buildSectionTitle(context, context.tr.agreements),
            AppGaps.gap16,
            _buildServiceGrid(context, [
              _ServiceItem(
                icon: Icons.description,
                title: context.tr.quotations,
                onTap: () {
                  const QuotationsScreen().navigate;
                },
              ),
              _ServiceItem(
                icon: Icons.card_membership,
                title: context.tr.licenses,
                onTap: () {
                  const LicensesScreen().navigate;
                },
              ),
              _ServiceItem(
                icon: Icons.subscriptions,
                title: context.tr.subscriptions,
                onTap: () {
                  // Navigate to subscriptions
                },
              ),
              _ServiceItem(
                icon: Icons.build,
                title: context.tr.maintenance,
                onTap: () {
                  // Navigate to maintenance
                },
              ),
            ]),

            AppGaps.gap32,

            // مركز المساعدة Section
            _buildSectionTitle(context, context.tr.helpCenter),
            AppGaps.gap16,
            _buildServiceGrid(context, [
              _ServiceItem(
                icon: Icons.confirmation_number,
                title: context.tr.myTickets,
                onTap: () {
                  // Navigate to my tickets
                },
              ),
              _ServiceItem(
                icon: Icons.archive,
                title: context.tr.myArchivedTickets,
                onTap: () {
                  // Navigate to my archived tickets
                },
              ),
              _ServiceItem(
                icon: Icons.all_inclusive,
                title: context.tr.allTickets,
                onTap: () {
                  // Navigate to all tickets
                },
              ),
              _ServiceItem(
                icon: Icons.folder_special,
                title: context.tr.allArchivedTickets,
                onTap: () {
                  // Navigate to all archived tickets
                },
              ),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    return Text(
      title,
      style: AppTextStyles.title.copyWith(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: ColorManager.primaryColor,
      ),
    );
  }

  Widget _buildServiceGrid(BuildContext context, List<_ServiceItem> items) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.0,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return _buildServiceCard(context, items[index]);
      },
    );
  }

  Widget _buildServiceCard(BuildContext context, _ServiceItem item) {
    return InkWell(
      onTap: item.onTap,
      borderRadius: BorderRadius.circular(AppRadius.radius12),
      child: Container(
        decoration: BoxDecoration(
          color: ColorManager.lightGrey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(AppRadius.radius12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              item.icon,
              size: 28,
              color: ColorManager.primaryColor,
            ),
            AppGaps.gap8,
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Text(
                item.title,
                textAlign: TextAlign.center,
                style: AppTextStyles.labelSmall.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ServiceItem {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const _ServiceItem({
    required this.icon,
    required this.title,
    required this.onTap,
  });
}
