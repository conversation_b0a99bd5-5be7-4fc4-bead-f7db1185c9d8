import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/app.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xr_helper/xr_helper.dart';

import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await GetStorageService.init();

  HttpOverrides.global = MyHttpOverrides();

  final deviceInfo = DeviceInfoPlugin();
  if (!kIsWeb) {
    if (Platform.isAndroid &&
        (await deviceInfo.androidInfo).version.sdkInt > 29) {
      await Permission.manageExternalStorage.request();
    } else {
      await Permission.storage.request();
    }
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);
  }

  NotificationService.init();

  runApp(const ProviderScope(child: BaseApp()));
}

//! Tab after client information (الخدمات - سجل الاجراءات)
//! Change if تحت الاجراء first تعديل العرض - تسعير العرض
//! Change if موافقة العميل first تعديل العرض - تسعير العرض and show by default send pics
//! When click اعتماد العميل make optional to upload file and click button
//! to be under each other instead of scroll
//! From تراخيص go to add pricing too
//! Bottom Navigation on all pages
//! Make animation from navigation bar and remove splash of it
//! Make my profile as sliver and go in top after scroll and tile name profile data

//! Choose when print ar or en

//?   "tasks": {
//             "active": 0,
//             "finished": 6
//         },
//         "tickets": {
//             "active": 0,
//             "finished": 2
//         },
//         "contracts": {
//             "near_expire": 0,
//             "is_expired": 7
//         },

//! Home Updates => asks title under it 2 container rows to view active tasks - finished tasks // Active Tickets - Finished Tickets // Near To Expire - Has Expired
//! Handle Tabs
// ! (الرئيسية - ملفي - الخدمات - المبيعات)
//! Handle New Cards like image attached and circles for program managment
//! Make actions in quotations (Edit icon - Eye for View) Eye will make new pricing and send to client - each one from api
//! Add search by more than thing like title - if contains in code too
//! Make staus phone bar blue
//! Make sure to navigate from inside pages bottom navigation bar
//! Handle hint instead of label and make title of each field in Add Client & Add Quotation
//! Make Steps for Add Quotations

//? and make sure any translation to add in @/lib/l10n/intl_en.arb @/lib/l10n/intl_ar.arb and styles from @/packages/xr_helper/lib/src/resources/theme/text_styles.dart and gaps from @/packages/xr_helper/lib/src/resources/spaces/gap_spaces.dart and spaces in padding from @/packages/xr_helper/lib/src/resources/app_spaces.dart

//Make bottom navigation as those for now
//
// // ! (الرئيسية - ملفي - الخدمات - المبيعات)
//
// and make sure any translation to add in @/lib/l10n/intl_en.arb @/lib/l10n/intl_ar.arb and styles from @/packages/xr_helper/lib/src/resources/theme/text_styles.dart and gaps from @/packages/xr_helper/lib/src/resources/spaces/gap_spaces.dart and spaces in padding from @/packages/xr_helper/lib/src/resources/app_spaces.dart
//
// handle to remove this section from home
//
// _buildAgreementsSection(context),
//
// @/lib/src/screens/home/<USER>/home_screen.dart
//
// to be in new الخدمات page that will be in bottom navigation
//
// and this page will have more than title then under them section like the following make sure there will be like that
//
// العملاء
// قائمه العملاء + الاجتماعات + اراء العملاء
//
// الاتفاقيات
// عروض اسعار
// التراخيص الاشتراكات
// الصيانه
//
// مركز المساعده
// تذاكري
// تذاكري المؤرشفة
// جميع التذاكر
// جميع التذاكر المؤرشفة
//
//
// another thing
//
// Home Updates => asks title under it 2 container rows to view active tasks - finished tasks // Active Tickets - Finished Tickets // Near To Expire - Has Expired
//
// to be 2 cards with good UI for each section in home
//
// this is current home response handle based on it
//
// {
//     "success": true,
//     "dt": {
//         "employee": {
//             "empPIC": "http://app.iv4it.com/assets/users/1753270127_scaled_52.jpg",
//             "empName": "علاء باهي",
//             "empJob": "دعم فني",
//             "work_timings": {
//                 "start": "09:00 ص",
//                 "end": "05:00 م"
//             }
//         },
//         "attendance": {
//             "allow_attend": true,
//             "attend": null,
//             "checkout": null
//         },
//         "tasks": {
//             "active": 0,
//             "finished": 6
//         },
//         "tickets": {
//             "active": 0,
//             "finished": 2
//         },
//         "contracts": {
//             "near_expire": 0,
//             "is_expired": 7
//         },
//         "current_month_stats": {
//             "total_complete_attends": 5,
//             "total_incomplete_attends": 5,
//             "total_absents": 17,
//             "total_official_holidays": 0,
//             "total_vacation_leaves": 8,
//             "total_request_leaves": 0,
//             "total_sick_leaves": 0
//         }
//     }
// }
//
// @/lib/src/screens/home/<USER>/home_model.dart
//
// and this is current quotations response handle @/lib/src/screens/quotations based on it
//
// {
//     "success": true,
//     "can": {
//         "add": true
//     },
//     "dt": [
//         {
//             "ipd": 423,
//             "unicode": "AB\\016\\25\\07\\28",
//             "createat": "2025-07-28 16:42:43",
//             "client": "شركة حلول البرمجيات المبتكرة لتقنية المعلومات",
//             "subscriptions": {
//                 "1": [
//                     {
//                         "service_name": "نظام لافـــــا",
//                         "subservice_name": "الحسابات العامة"
//                     }
//                 ]
//             },
//             "status": {
//                 "id": 1,
//                 "status": "تحت الإجراء",
//                 "color": "info",
//                 "message": null
//             },
//             "can_edit": true
//         },
//         {
//             "ipd": 422,
//             "unicode": "AB\\015\\25\\07\\27",
//             "createat": "2025-07-27 15:42:02",
//             "client": "شركة حلول البرمجيات المبتكرة لتقنية المعلومات",
//             "subscriptions": {
//                 "1": [
//                     {
//                         "service_name": "نظام لافـــــا",
//                         "subservice_name": "الحسابات العامة"
//                     }
//                 ]
//             },
//             "status": {
//                 "id": 1,
//                 "status": "تحت الإجراء",
//                 "color": "info",
//                 "message": null
//             },
//             "can_edit": true
//         },
//
// @/lib/src/screens/quotations/models/quotation_model.dart @/lib/src/screens/quotations/repositories/quotation_repository.dart
//
// and add in @/lib/src/screens/quotations/view/quotations_screen.dart
//
// Search by more than thing like title or if contains in search in it's code too
//
// and top header native in mobile is almost white and transparent and don't show so handle that
//
// to be blue not transparent like now @/lib/main.dart
//
// another thing handle @/lib/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart to show inside @/lib/src/screens/quotations/view/quotations_screen.dart correct and navigate to new pages because now when click on it not navigating the screen @/lib/src/core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart
//
// and handle @/lib/src/screens/quotations/view/add_quotation_screen.dart
//
// to be add in steps first step should choose the client then when click next choose parent products then next step choose their sub products and handle to send based on what api exist now to send the same response with this new way and handle another thing if product don't have sub products just skip and finish the add and send it's id in sub product id and product id
//
// and handle @/lib/src/screens/clients/view/add_client_screen.dart to work with this @/lib/src/core/shared/widgets/fields/text_field.dart and add title and hint only in each field
