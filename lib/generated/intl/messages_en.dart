// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(name) => "Welcome, ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "absents": MessageLookupByLibrary.simpleMessage("Absents"),
    "actions": MessageLookupByLibrary.simpleMessage("Actions"),
    "active": MessageLookupByLibrary.simpleMessage("Active"),
    "activeTasks": MessageLookupByLibrary.simpleMessage("Active Tasks"),
    "activeTickets": MessageLookupByLibrary.simpleMessage("Active Tickets"),
    "addAttachment": MessageLookupByLibrary.simpleMessage("Add Attachment"),
    "addClient": MessageLookupByLibrary.simpleMessage("Add Client"),
    "addLeaveRequest": MessageLookupByLibrary.simpleMessage(
      "Add Leave Request",
    ),
    "addNewTicket": MessageLookupByLibrary.simpleMessage("Add New Ticket"),
    "addNote": MessageLookupByLibrary.simpleMessage("Add Note"),
    "agreements": MessageLookupByLibrary.simpleMessage("Agreements"),
    "allArchivedTickets": MessageLookupByLibrary.simpleMessage(
      "All Archived Tickets",
    ),
    "allTickets": MessageLookupByLibrary.simpleMessage("All Tickets"),
    "approveClient": MessageLookupByLibrary.simpleMessage("Approve Client"),
    "approved": MessageLookupByLibrary.simpleMessage("Approved"),
    "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
    "arabicPrint": MessageLookupByLibrary.simpleMessage("Arabic Print"),
    "archived": MessageLookupByLibrary.simpleMessage("Archived"),
    "archivedTickets": MessageLookupByLibrary.simpleMessage("Archived Tickets"),
    "areYouSureDeleteLeave": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this leave request?",
    ),
    "areYouSureYouWantTo": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to",
    ),
    "areYouSureYouWantToLogout": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to logout?",
    ),
    "ascending": MessageLookupByLibrary.simpleMessage("Ascending"),
    "attachment": MessageLookupByLibrary.simpleMessage("Attachment"),
    "attachmentOptional": MessageLookupByLibrary.simpleMessage(
      "Attachment (Optional)",
    ),
    "attendanceTime": MessageLookupByLibrary.simpleMessage("Attendance Time"),
    "authenticateToCheckIn": MessageLookupByLibrary.simpleMessage(
      "Please authenticate to check in",
    ),
    "authenticateToCheckOut": MessageLookupByLibrary.simpleMessage(
      "Please authenticate to check out",
    ),
    "authenticationFailed": MessageLookupByLibrary.simpleMessage(
      "Authentication failed",
    ),
    "biometricNotAvailable": MessageLookupByLibrary.simpleMessage(
      "Biometric authentication is not available on this device",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("Camera"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "changeLanguage": MessageLookupByLibrary.simpleMessage("Change Language"),
    "checkIn": MessageLookupByLibrary.simpleMessage("Check In"),
    "checkInFailed": MessageLookupByLibrary.simpleMessage("Check-in failed"),
    "checkInSuccessful": MessageLookupByLibrary.simpleMessage(
      "Check-in successful",
    ),
    "checkOut": MessageLookupByLibrary.simpleMessage("Check Out"),
    "checkOutFailed": MessageLookupByLibrary.simpleMessage("Check-out failed"),
    "checkOutSuccessful": MessageLookupByLibrary.simpleMessage(
      "Check-out successful",
    ),
    "client": MessageLookupByLibrary.simpleMessage("Client"),
    "clientAdded": MessageLookupByLibrary.simpleMessage(
      "Client added successfully",
    ),
    "clientApproval": MessageLookupByLibrary.simpleMessage("Client Approval"),
    "clientID": MessageLookupByLibrary.simpleMessage("Client ID"),
    "clientInformation": MessageLookupByLibrary.simpleMessage(
      "Client Information",
    ),
    "clientName": MessageLookupByLibrary.simpleMessage("Client Name"),
    "clientNote": MessageLookupByLibrary.simpleMessage("Client Note"),
    "clientOpinions": MessageLookupByLibrary.simpleMessage("Client Opinions"),
    "clientSelection": MessageLookupByLibrary.simpleMessage("Client Selection"),
    "clientStatus": MessageLookupByLibrary.simpleMessage("Client Status"),
    "clients": MessageLookupByLibrary.simpleMessage("Clients"),
    "clientsList": MessageLookupByLibrary.simpleMessage("Clients List"),
    "companyName": MessageLookupByLibrary.simpleMessage("Company Name"),
    "companyNameCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "Company name cannot be empty",
    ),
    "companyRegistration": MessageLookupByLibrary.simpleMessage(
      "Company Registration",
    ),
    "completeAttends": MessageLookupByLibrary.simpleMessage("Complete Attends"),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "contractCode": MessageLookupByLibrary.simpleMessage("Contract Code"),
    "contracts": MessageLookupByLibrary.simpleMessage("Contracts"),
    "contractsExpiring": MessageLookupByLibrary.simpleMessage(
      "Contracts Almost Expired",
    ),
    "cost": MessageLookupByLibrary.simpleMessage("Cost"),
    "createdAt": MessageLookupByLibrary.simpleMessage("Created At"),
    "createdBy": MessageLookupByLibrary.simpleMessage("Created By"),
    "createdDate": MessageLookupByLibrary.simpleMessage("Created Date"),
    "currentTime": MessageLookupByLibrary.simpleMessage("Current Time"),
    "customerMeetings": MessageLookupByLibrary.simpleMessage(
      "Customer Meetings",
    ),
    "customerOpinions": MessageLookupByLibrary.simpleMessage(
      "Customer Opinions",
    ),
    "customers": MessageLookupByLibrary.simpleMessage("Customers"),
    "customersList": MessageLookupByLibrary.simpleMessage("Customers List"),
    "dark": MessageLookupByLibrary.simpleMessage("Dark"),
    "days": MessageLookupByLibrary.simpleMessage("Days"),
    "deleteLeaveRequest": MessageLookupByLibrary.simpleMessage(
      "Delete Leave Request",
    ),
    "descending": MessageLookupByLibrary.simpleMessage("Descending"),
    "description": MessageLookupByLibrary.simpleMessage("Description"),
    "discount": MessageLookupByLibrary.simpleMessage("Discount"),
    "done": MessageLookupByLibrary.simpleMessage("Done"),
    "edit": MessageLookupByLibrary.simpleMessage("Edit"),
    "editLeaveRequest": MessageLookupByLibrary.simpleMessage(
      "Edit Leave Request",
    ),
    "editProfile": MessageLookupByLibrary.simpleMessage("Edit Profile"),
    "editQuotation": MessageLookupByLibrary.simpleMessage("Edit Quotation"),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "empJob": MessageLookupByLibrary.simpleMessage("Job Title"),
    "empName": MessageLookupByLibrary.simpleMessage("Employee Name"),
    "endDate": MessageLookupByLibrary.simpleMessage("End Date"),
    "english": MessageLookupByLibrary.simpleMessage("English"),
    "englishPrint": MessageLookupByLibrary.simpleMessage("English Print"),
    "enter": MessageLookupByLibrary.simpleMessage("Enter"),
    "enterCost": MessageLookupByLibrary.simpleMessage("Enter Cost"),
    "enterDiscount": MessageLookupByLibrary.simpleMessage("Enter Discount"),
    "enterInstallments": MessageLookupByLibrary.simpleMessage(
      "Enter Installments",
    ),
    "enterMonths": MessageLookupByLibrary.simpleMessage("Enter Months"),
    "enterUserLimit": MessageLookupByLibrary.simpleMessage("Enter user limit"),
    "expired": MessageLookupByLibrary.simpleMessage("Expired"),
    "expiryDate": MessageLookupByLibrary.simpleMessage("Expiry Date"),
    "extraUserCost": MessageLookupByLibrary.simpleMessage("Extra User Cost"),
    "failedToGeneratePdf": MessageLookupByLibrary.simpleMessage(
      "Failed to generate PDF",
    ),
    "failedToLoadClients": MessageLookupByLibrary.simpleMessage(
      "Failed to load clients",
    ),
    "failedToLoadProducts": MessageLookupByLibrary.simpleMessage(
      "Failed to load products",
    ),
    "failedToSavePricing": MessageLookupByLibrary.simpleMessage(
      "Failed to save pricing",
    ),
    "failedToUpdateQuotation": MessageLookupByLibrary.simpleMessage(
      "Failed to update quotation",
    ),
    "failedToUpdateStatus": MessageLookupByLibrary.simpleMessage(
      "Failed to update status",
    ),
    "finish": MessageLookupByLibrary.simpleMessage("Finish"),
    "finishedTasks": MessageLookupByLibrary.simpleMessage("Finished Tasks"),
    "finishedTickets": MessageLookupByLibrary.simpleMessage("Finished Tickets"),
    "fromDate": MessageLookupByLibrary.simpleMessage("From Date"),
    "fromDateCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "From date cannot be empty",
    ),
    "gallery": MessageLookupByLibrary.simpleMessage("Gallery"),
    "generatingPdf": MessageLookupByLibrary.simpleMessage("Generating PDF..."),
    "hasExpired": MessageLookupByLibrary.simpleMessage("Has Expired"),
    "hasLimitUsers": MessageLookupByLibrary.simpleMessage("Has Limit Users"),
    "hasUserLimit": MessageLookupByLibrary.simpleMessage("Has User Limit"),
    "helpCenter": MessageLookupByLibrary.simpleMessage("Help Center"),
    "historyTab": MessageLookupByLibrary.simpleMessage("History"),
    "home": MessageLookupByLibrary.simpleMessage("Home"),
    "homeUpdates": MessageLookupByLibrary.simpleMessage("Home Updates"),
    "incompleteAttends": MessageLookupByLibrary.simpleMessage(
      "Incomplete Attends",
    ),
    "initialAmount": MessageLookupByLibrary.simpleMessage("Initial Amount"),
    "issue": MessageLookupByLibrary.simpleMessage("Issue"),
    "issueLicense": MessageLookupByLibrary.simpleMessage("Issue License"),
    "issuedAt": MessageLookupByLibrary.simpleMessage("Issued At"),
    "issuerEmail": MessageLookupByLibrary.simpleMessage("Issuer Email"),
    "issuerName": MessageLookupByLibrary.simpleMessage("Issuer Name"),
    "issuerPhone": MessageLookupByLibrary.simpleMessage("Issuer Phone"),
    "itsGreatToSeeYou": MessageLookupByLibrary.simpleMessage(
      "It\'s great to see you",
    ),
    "language": MessageLookupByLibrary.simpleMessage("Language"),
    "leads": MessageLookupByLibrary.simpleMessage("Leads"),
    "leaveRequestAdded": MessageLookupByLibrary.simpleMessage(
      "Leave request added successfully",
    ),
    "leaveRequestDeleted": MessageLookupByLibrary.simpleMessage(
      "Leave request deleted successfully",
    ),
    "leaveRequestUpdated": MessageLookupByLibrary.simpleMessage(
      "Leave request updated successfully",
    ),
    "leaveRequests": MessageLookupByLibrary.simpleMessage("Leave Requests"),
    "leaveType": MessageLookupByLibrary.simpleMessage("Leave Type"),
    "leaves": MessageLookupByLibrary.simpleMessage("Leaves"),
    "licenses": MessageLookupByLibrary.simpleMessage("Licenses"),
    "licensesSubscriptions": MessageLookupByLibrary.simpleMessage(
      "Licenses & Subscriptions",
    ),
    "light": MessageLookupByLibrary.simpleMessage("Light"),
    "loadingClients": MessageLookupByLibrary.simpleMessage(
      "Loading clients...",
    ),
    "location": MessageLookupByLibrary.simpleMessage("Location"),
    "login": MessageLookupByLibrary.simpleMessage("Login"),
    "logout": MessageLookupByLibrary.simpleMessage("Logout"),
    "maintenance": MessageLookupByLibrary.simpleMessage("Maintenance"),
    "meetings": MessageLookupByLibrary.simpleMessage("Meetings"),
    "mobile": MessageLookupByLibrary.simpleMessage("Mobile"),
    "monthlyStats": MessageLookupByLibrary.simpleMessage("Monthly Statistics"),
    "months": MessageLookupByLibrary.simpleMessage("Months"),
    "myArchivedTickets": MessageLookupByLibrary.simpleMessage(
      "My Archived Tickets",
    ),
    "myProfile": MessageLookupByLibrary.simpleMessage("My Profile"),
    "mySubscriptions": MessageLookupByLibrary.simpleMessage("My Subscriptions"),
    "myTickets": MessageLookupByLibrary.simpleMessage("My Tickets"),
    "nearExpire": MessageLookupByLibrary.simpleMessage("Near Expire"),
    "nearToExpire": MessageLookupByLibrary.simpleMessage("Near To Expire"),
    "newReplyOnTicket": MessageLookupByLibrary.simpleMessage(
      "New reply on ticket",
    ),
    "next": MessageLookupByLibrary.simpleMessage("Next"),
    "noDataFound": MessageLookupByLibrary.simpleMessage("No data found"),
    "noProductsAvailable": MessageLookupByLibrary.simpleMessage(
      "No products available",
    ),
    "noRepliesFound": MessageLookupByLibrary.simpleMessage("No replies found"),
    "noSubProductsSelected": MessageLookupByLibrary.simpleMessage(
      "No sub-products selected",
    ),
    "note": MessageLookupByLibrary.simpleMessage("Note"),
    "noteRequired": MessageLookupByLibrary.simpleMessage("Note is required"),
    "of1": MessageLookupByLibrary.simpleMessage("of"),
    "officialHolidays": MessageLookupByLibrary.simpleMessage(
      "Official Holidays",
    ),
    "password": MessageLookupByLibrary.simpleMessage("Password"),
    "paymentDetails": MessageLookupByLibrary.simpleMessage("Payment Details"),
    "paymentEvery": MessageLookupByLibrary.simpleMessage("Payment Every"),
    "paymentInstallments": MessageLookupByLibrary.simpleMessage(
      "Payment Installments",
    ),
    "paymentStartDate": MessageLookupByLibrary.simpleMessage(
      "Payment Start Date",
    ),
    "pdfGeneratedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "PDF generated successfully",
    ),
    "pending": MessageLookupByLibrary.simpleMessage("Pending"),
    "pickImage": MessageLookupByLibrary.simpleMessage("Pick Image"),
    "pleaseCompleteAllSteps": MessageLookupByLibrary.simpleMessage(
      "Please complete all steps",
    ),
    "pleaseSelectAtLeastOneProduct": MessageLookupByLibrary.simpleMessage(
      "Please select at least one product",
    ),
    "pleaseSelectAtLeastOneSubProduct": MessageLookupByLibrary.simpleMessage(
      "Please select at least one sub-product",
    ),
    "pleaseSelectClientFirst": MessageLookupByLibrary.simpleMessage(
      "Please select a client first",
    ),
    "previous": MessageLookupByLibrary.simpleMessage("Previous"),
    "price": MessageLookupByLibrary.simpleMessage("Price"),
    "priceCalculation": MessageLookupByLibrary.simpleMessage(
      "Price Calculation",
    ),
    "pricing": MessageLookupByLibrary.simpleMessage("Pricing"),
    "pricingSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Pricing saved successfully",
    ),
    "print": MessageLookupByLibrary.simpleMessage("Print"),
    "productId": MessageLookupByLibrary.simpleMessage("Product ID"),
    "productSelected": MessageLookupByLibrary.simpleMessage("product selected"),
    "productSelection": MessageLookupByLibrary.simpleMessage(
      "Product Selection",
    ),
    "productsSelected": MessageLookupByLibrary.simpleMessage(
      "products selected",
    ),
    "profile": MessageLookupByLibrary.simpleMessage("Profile"),
    "profilePicture": MessageLookupByLibrary.simpleMessage("Profile Picture"),
    "profileUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Profile updated successfully",
    ),
    "quotationCode": MessageLookupByLibrary.simpleMessage("Quotation Code"),
    "quotationDetails": MessageLookupByLibrary.simpleMessage(
      "Quotation Details",
    ),
    "quotationPricing": MessageLookupByLibrary.simpleMessage(
      "Quotation Pricing",
    ),
    "quotationSaved": MessageLookupByLibrary.simpleMessage(
      "Quotation saved successfully",
    ),
    "quotationStatus": MessageLookupByLibrary.simpleMessage("Quotation Status"),
    "quotationSummary": MessageLookupByLibrary.simpleMessage(
      "Quotation Summary",
    ),
    "quotationSummaryNote": MessageLookupByLibrary.simpleMessage(
      "Please review all the information above before submitting the quotation. Once submitted, you can modify pricing and other details later.",
    ),
    "quotationUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Quotation updated successfully",
    ),
    "quotations": MessageLookupByLibrary.simpleMessage("Quotations"),
    "quotationsList": MessageLookupByLibrary.simpleMessage("Quotations List"),
    "reason": MessageLookupByLibrary.simpleMessage("Reason"),
    "reasonCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "Reason cannot be empty",
    ),
    "recentActiveTickets": MessageLookupByLibrary.simpleMessage(
      "Recent Active Tickets",
    ),
    "register": MessageLookupByLibrary.simpleMessage("Register"),
    "rejected": MessageLookupByLibrary.simpleMessage("Rejected"),
    "remainingDays": MessageLookupByLibrary.simpleMessage("Remaining Days"),
    "remainingMaintenance": MessageLookupByLibrary.simpleMessage(
      "Remaining Maintenance",
    ),
    "repliedOnTheTicket": MessageLookupByLibrary.simpleMessage(
      "Replied on the ticket",
    ),
    "replies": MessageLookupByLibrary.simpleMessage("Replies"),
    "reply": MessageLookupByLibrary.simpleMessage("Reply"),
    "replyCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "Reply cannot be empty",
    ),
    "replySentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Reply sent successfully",
    ),
    "reports": MessageLookupByLibrary.simpleMessage("Reports"),
    "request": MessageLookupByLibrary.simpleMessage("Request"),
    "requestLeave": MessageLookupByLibrary.simpleMessage("Request Leave"),
    "requestLeaves": MessageLookupByLibrary.simpleMessage("Request Leaves"),
    "responsibleEmail": MessageLookupByLibrary.simpleMessage(
      "Responsible Email",
    ),
    "responsibleJob": MessageLookupByLibrary.simpleMessage("Responsible Job"),
    "responsibleName": MessageLookupByLibrary.simpleMessage("Responsible Name"),
    "responsibleNameCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "Responsible name cannot be empty",
    ),
    "responsiblePhone": MessageLookupByLibrary.simpleMessage(
      "Responsible Phone",
    ),
    "responsiblePhoneCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "Responsible phone cannot be empty",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "sales": MessageLookupByLibrary.simpleMessage("Sales"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "savePricing": MessageLookupByLibrary.simpleMessage("Save Pricing"),
    "saveQuotation": MessageLookupByLibrary.simpleMessage("Save Quotation"),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "searchByCodeOrTitle": MessageLookupByLibrary.simpleMessage(
      "Search by code or title",
    ),
    "selectClient": MessageLookupByLibrary.simpleMessage("Select Client"),
    "selectDate": MessageLookupByLibrary.simpleMessage("Select Date"),
    "selectLeaveType": MessageLookupByLibrary.simpleMessage(
      "Select Leave Type",
    ),
    "selectParentProduct": MessageLookupByLibrary.simpleMessage(
      "Select Parent Product",
    ),
    "selectPrintLanguage": MessageLookupByLibrary.simpleMessage(
      "Select Print Language",
    ),
    "selectProduct": MessageLookupByLibrary.simpleMessage("Select Product"),
    "selectProductsAndServices": MessageLookupByLibrary.simpleMessage(
      "Select Products & Services",
    ),
    "selectSubProduct": MessageLookupByLibrary.simpleMessage(
      "Select Sub Product",
    ),
    "selectSubProducts": MessageLookupByLibrary.simpleMessage(
      "Select Sub Products",
    ),
    "selectSubServices": MessageLookupByLibrary.simpleMessage(
      "Select Sub-Services",
    ),
    "selectedClient": MessageLookupByLibrary.simpleMessage("Selected Client"),
    "selectedProducts": MessageLookupByLibrary.simpleMessage(
      "Selected Products",
    ),
    "send": MessageLookupByLibrary.simpleMessage("Send"),
    "serviceName": MessageLookupByLibrary.simpleMessage("Service Name"),
    "services": MessageLookupByLibrary.simpleMessage("Services"),
    "servicesPricing": MessageLookupByLibrary.simpleMessage("Services Pricing"),
    "servicesTab": MessageLookupByLibrary.simpleMessage("Services/Products"),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "sickLeave": MessageLookupByLibrary.simpleMessage("Sick Leave"),
    "sickLeaves": MessageLookupByLibrary.simpleMessage("Sick Leaves"),
    "softwareManagement": MessageLookupByLibrary.simpleMessage(
      "Software Management",
    ),
    "somethingWentWrong": MessageLookupByLibrary.simpleMessage(
      "Something went wrong",
    ),
    "sortByDate": MessageLookupByLibrary.simpleMessage("Sort by Date"),
    "startDate": MessageLookupByLibrary.simpleMessage("Start Date"),
    "status": MessageLookupByLibrary.simpleMessage("Status"),
    "statusHistory": MessageLookupByLibrary.simpleMessage("Status History"),
    "statusUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Status updated successfully",
    ),
    "step": MessageLookupByLibrary.simpleMessage("Step"),
    "subProductId": MessageLookupByLibrary.simpleMessage("Sub Product ID"),
    "subProductSelection": MessageLookupByLibrary.simpleMessage(
      "Sub Product Selection",
    ),
    "subProducts": MessageLookupByLibrary.simpleMessage("Sub Products"),
    "subServiceName": MessageLookupByLibrary.simpleMessage("Sub Service Name"),
    "subServiceSelected": MessageLookupByLibrary.simpleMessage(
      "sub-service selected",
    ),
    "subServices": MessageLookupByLibrary.simpleMessage("Sub Services"),
    "subServicesSelected": MessageLookupByLibrary.simpleMessage(
      "sub-services selected",
    ),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "subscriptions": MessageLookupByLibrary.simpleMessage("Subscriptions"),
    "subtotal": MessageLookupByLibrary.simpleMessage("Subtotal"),
    "summary": MessageLookupByLibrary.simpleMessage("Summary"),
    "systemSetting": MessageLookupByLibrary.simpleMessage("System Setting"),
    "theme": MessageLookupByLibrary.simpleMessage("Theme"),
    "tickets": MessageLookupByLibrary.simpleMessage("Tickets"),
    "toDate": MessageLookupByLibrary.simpleMessage("To Date"),
    "toDateCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "To date cannot be empty",
    ),
    "total": MessageLookupByLibrary.simpleMessage("Total"),
    "totalTickets": MessageLookupByLibrary.simpleMessage("Total Tickets"),
    "updateProfile": MessageLookupByLibrary.simpleMessage("Update Profile"),
    "updateQuotation": MessageLookupByLibrary.simpleMessage("Update Quotation"),
    "updateStatus": MessageLookupByLibrary.simpleMessage("Update Status"),
    "userEmail": MessageLookupByLibrary.simpleMessage("<EMAIL>"),
    "userLimit": MessageLookupByLibrary.simpleMessage("User Limit"),
    "userName": MessageLookupByLibrary.simpleMessage("User Name"),
    "username": MessageLookupByLibrary.simpleMessage("Username"),
    "users": MessageLookupByLibrary.simpleMessage("Users"),
    "vacationLeave": MessageLookupByLibrary.simpleMessage("Vacation Leave"),
    "vacationLeaves": MessageLookupByLibrary.simpleMessage("Vacation Leaves"),
    "validityDays": MessageLookupByLibrary.simpleMessage("Validity Days"),
    "vat": MessageLookupByLibrary.simpleMessage("VAT"),
    "welcomeBack": MessageLookupByLibrary.simpleMessage("Welcome back"),
    "welcomeBackLine": MessageLookupByLibrary.simpleMessage("Welcome\nback"),
    "welcomeWithName": m0,
    "workTime": MessageLookupByLibrary.simpleMessage("Work Time"),
  };
}
