// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `No data found`
  String get noDataFound {
    return Intl.message(
      'No data found',
      name: 'noDataFound',
      desc: '',
      args: [],
    );
  }

  /// `Enter`
  String get enter {
    return Intl.message('Enter', name: 'enter', desc: '', args: []);
  }

  /// `Pick Image`
  String get pickImage {
    return Intl.message('Pick Image', name: 'pickImage', desc: '', args: []);
  }

  /// `Search`
  String get search {
    return Intl.message('Search', name: 'search', desc: '', args: []);
  }

  /// `Welcome, {name}`
  String welcomeWithName(Object name) {
    return Intl.message(
      'Welcome, $name',
      name: 'welcomeWithName',
      desc: '',
      args: [name],
    );
  }

  /// `Home`
  String get home {
    return Intl.message('Home', name: 'home', desc: '', args: []);
  }

  /// `Register`
  String get register {
    return Intl.message('Register', name: 'register', desc: '', args: []);
  }

  /// `Login`
  String get login {
    return Intl.message('Login', name: 'login', desc: '', args: []);
  }

  /// `Remaining Maintenance`
  String get remainingMaintenance {
    return Intl.message(
      'Remaining Maintenance',
      name: 'remainingMaintenance',
      desc: '',
      args: [],
    );
  }

  /// `My Subscriptions`
  String get mySubscriptions {
    return Intl.message(
      'My Subscriptions',
      name: 'mySubscriptions',
      desc: '',
      args: [],
    );
  }

  /// `Days`
  String get days {
    return Intl.message('Days', name: 'days', desc: '', args: []);
  }

  /// `Issuer Name`
  String get issuerName {
    return Intl.message('Issuer Name', name: 'issuerName', desc: '', args: []);
  }

  /// `Issuer Email`
  String get issuerEmail {
    return Intl.message(
      'Issuer Email',
      name: 'issuerEmail',
      desc: '',
      args: [],
    );
  }

  /// `Issuer Phone`
  String get issuerPhone {
    return Intl.message(
      'Issuer Phone',
      name: 'issuerPhone',
      desc: '',
      args: [],
    );
  }

  /// `Attachment`
  String get attachment {
    return Intl.message('Attachment', name: 'attachment', desc: '', args: []);
  }

  /// `Description`
  String get description {
    return Intl.message('Description', name: 'description', desc: '', args: []);
  }

  /// `Submit`
  String get submit {
    return Intl.message('Submit', name: 'submit', desc: '', args: []);
  }

  /// `Active`
  String get active {
    return Intl.message('Active', name: 'active', desc: '', args: []);
  }

  /// `Archived`
  String get archived {
    return Intl.message('Archived', name: 'archived', desc: '', args: []);
  }

  /// `Recent Active Tickets`
  String get recentActiveTickets {
    return Intl.message(
      'Recent Active Tickets',
      name: 'recentActiveTickets',
      desc: '',
      args: [],
    );
  }

  /// `No replies found`
  String get noRepliesFound {
    return Intl.message(
      'No replies found',
      name: 'noRepliesFound',
      desc: '',
      args: [],
    );
  }

  /// `Replies`
  String get replies {
    return Intl.message('Replies', name: 'replies', desc: '', args: []);
  }

  /// `Total Tickets`
  String get totalTickets {
    return Intl.message(
      'Total Tickets',
      name: 'totalTickets',
      desc: '',
      args: [],
    );
  }

  /// `Start Date`
  String get startDate {
    return Intl.message('Start Date', name: 'startDate', desc: '', args: []);
  }

  /// `End Date`
  String get endDate {
    return Intl.message('End Date', name: 'endDate', desc: '', args: []);
  }

  /// `Are you sure you want to logout?`
  String get areYouSureYouWantToLogout {
    return Intl.message(
      'Are you sure you want to logout?',
      name: 'areYouSureYouWantToLogout',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get status {
    return Intl.message('Status', name: 'status', desc: '', args: []);
  }

  /// `Reply`
  String get reply {
    return Intl.message('Reply', name: 'reply', desc: '', args: []);
  }

  /// `Gallery`
  String get gallery {
    return Intl.message('Gallery', name: 'gallery', desc: '', args: []);
  }

  /// `Camera`
  String get camera {
    return Intl.message('Camera', name: 'camera', desc: '', args: []);
  }

  /// `Reply cannot be empty`
  String get replyCannotBeEmpty {
    return Intl.message(
      'Reply cannot be empty',
      name: 'replyCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout {
    return Intl.message('Logout', name: 'logout', desc: '', args: []);
  }

  /// `Reply sent successfully`
  String get replySentSuccessfully {
    return Intl.message(
      'Reply sent successfully',
      name: 'replySentSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get english {
    return Intl.message('English', name: 'english', desc: '', args: []);
  }

  /// `العربية`
  String get arabic {
    return Intl.message('العربية', name: 'arabic', desc: '', args: []);
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `Change Language`
  String get changeLanguage {
    return Intl.message(
      'Change Language',
      name: 'changeLanguage',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message('Password', name: 'password', desc: '', args: []);
  }

  /// `Save`
  String get save {
    return Intl.message('Save', name: 'save', desc: '', args: []);
  }

  /// `Welcome back`
  String get welcomeBack {
    return Intl.message(
      'Welcome back',
      name: 'welcomeBack',
      desc: '',
      args: [],
    );
  }

  /// `Welcome\nback`
  String get welcomeBackLine {
    return Intl.message(
      'Welcome\nback',
      name: 'welcomeBackLine',
      desc: '',
      args: [],
    );
  }

  /// `Reports`
  String get reports {
    return Intl.message('Reports', name: 'reports', desc: '', args: []);
  }

  /// `It's great to see you`
  String get itsGreatToSeeYou {
    return Intl.message(
      'It\'s great to see you',
      name: 'itsGreatToSeeYou',
      desc: '',
      args: [],
    );
  }

  /// `All Tickets`
  String get allTickets {
    return Intl.message('All Tickets', name: 'allTickets', desc: '', args: []);
  }

  /// `Username`
  String get username {
    return Intl.message('Username', name: 'username', desc: '', args: []);
  }

  /// `Sort by Date`
  String get sortByDate {
    return Intl.message('Sort by Date', name: 'sortByDate', desc: '', args: []);
  }

  /// `Replied on the ticket`
  String get repliedOnTheTicket {
    return Intl.message(
      'Replied on the ticket',
      name: 'repliedOnTheTicket',
      desc: '',
      args: [],
    );
  }

  /// `New reply on ticket`
  String get newReplyOnTicket {
    return Intl.message(
      'New reply on ticket',
      name: 'newReplyOnTicket',
      desc: '',
      args: [],
    );
  }

  /// `Ascending`
  String get ascending {
    return Intl.message('Ascending', name: 'ascending', desc: '', args: []);
  }

  /// `Confirm`
  String get confirm {
    return Intl.message('Confirm', name: 'confirm', desc: '', args: []);
  }

  /// `Descending`
  String get descending {
    return Intl.message('Descending', name: 'descending', desc: '', args: []);
  }

  /// `Request`
  String get request {
    return Intl.message('Request', name: 'request', desc: '', args: []);
  }

  /// `Issue`
  String get issue {
    return Intl.message('Issue', name: 'issue', desc: '', args: []);
  }

  /// `Add New Ticket`
  String get addNewTicket {
    return Intl.message(
      'Add New Ticket',
      name: 'addNewTicket',
      desc: '',
      args: [],
    );
  }

  /// `Current Time`
  String get currentTime {
    return Intl.message(
      'Current Time',
      name: 'currentTime',
      desc: '',
      args: [],
    );
  }

  /// `Attendance Time`
  String get attendanceTime {
    return Intl.message(
      'Attendance Time',
      name: 'attendanceTime',
      desc: '',
      args: [],
    );
  }

  /// `Location`
  String get location {
    return Intl.message('Location', name: 'location', desc: '', args: []);
  }

  /// `Monthly Statistics`
  String get monthlyStats {
    return Intl.message(
      'Monthly Statistics',
      name: 'monthlyStats',
      desc: '',
      args: [],
    );
  }

  /// `Client`
  String get client {
    return Intl.message('Client', name: 'client', desc: '', args: []);
  }

  /// `Complete Attends`
  String get completeAttends {
    return Intl.message(
      'Complete Attends',
      name: 'completeAttends',
      desc: '',
      args: [],
    );
  }

  /// `Incomplete Attends`
  String get incompleteAttends {
    return Intl.message(
      'Incomplete Attends',
      name: 'incompleteAttends',
      desc: '',
      args: [],
    );
  }

  /// `Absents`
  String get absents {
    return Intl.message('Absents', name: 'absents', desc: '', args: []);
  }

  /// `Official Holidays`
  String get officialHolidays {
    return Intl.message(
      'Official Holidays',
      name: 'officialHolidays',
      desc: '',
      args: [],
    );
  }

  /// `Vacation Leaves`
  String get vacationLeaves {
    return Intl.message(
      'Vacation Leaves',
      name: 'vacationLeaves',
      desc: '',
      args: [],
    );
  }

  /// `Request Leaves`
  String get requestLeaves {
    return Intl.message(
      'Request Leaves',
      name: 'requestLeaves',
      desc: '',
      args: [],
    );
  }

  /// `Sick Leaves`
  String get sickLeaves {
    return Intl.message('Sick Leaves', name: 'sickLeaves', desc: '', args: []);
  }

  /// `Active Tasks`
  String get activeTasks {
    return Intl.message(
      'Active Tasks',
      name: 'activeTasks',
      desc: '',
      args: [],
    );
  }

  /// `Finished Tasks`
  String get finishedTasks {
    return Intl.message(
      'Finished Tasks',
      name: 'finishedTasks',
      desc: '',
      args: [],
    );
  }

  /// `Check In`
  String get checkIn {
    return Intl.message('Check In', name: 'checkIn', desc: '', args: []);
  }

  /// `Check Out`
  String get checkOut {
    return Intl.message('Check Out', name: 'checkOut', desc: '', args: []);
  }

  /// `Work Time`
  String get workTime {
    return Intl.message('Work Time', name: 'workTime', desc: '', args: []);
  }

  /// `Contracts Almost Expired`
  String get contractsExpiring {
    return Intl.message(
      'Contracts Almost Expired',
      name: 'contractsExpiring',
      desc: '',
      args: [],
    );
  }

  /// `Contract Code`
  String get contractCode {
    return Intl.message(
      'Contract Code',
      name: 'contractCode',
      desc: '',
      args: [],
    );
  }

  /// `Client Name`
  String get clientName {
    return Intl.message('Client Name', name: 'clientName', desc: '', args: []);
  }

  /// `Something went wrong`
  String get somethingWentWrong {
    return Intl.message(
      'Something went wrong',
      name: 'somethingWentWrong',
      desc: '',
      args: [],
    );
  }

  /// `Expiry Date`
  String get expiryDate {
    return Intl.message('Expiry Date', name: 'expiryDate', desc: '', args: []);
  }

  /// `Remaining Days`
  String get remainingDays {
    return Intl.message(
      'Remaining Days',
      name: 'remainingDays',
      desc: '',
      args: [],
    );
  }

  /// `Biometric authentication is not available on this device`
  String get biometricNotAvailable {
    return Intl.message(
      'Biometric authentication is not available on this device',
      name: 'biometricNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Please authenticate to check in`
  String get authenticateToCheckIn {
    return Intl.message(
      'Please authenticate to check in',
      name: 'authenticateToCheckIn',
      desc: '',
      args: [],
    );
  }

  /// `Please authenticate to check out`
  String get authenticateToCheckOut {
    return Intl.message(
      'Please authenticate to check out',
      name: 'authenticateToCheckOut',
      desc: '',
      args: [],
    );
  }

  /// `Authentication failed`
  String get authenticationFailed {
    return Intl.message(
      'Authentication failed',
      name: 'authenticationFailed',
      desc: '',
      args: [],
    );
  }

  /// `Check-in successful`
  String get checkInSuccessful {
    return Intl.message(
      'Check-in successful',
      name: 'checkInSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Check-out successful`
  String get checkOutSuccessful {
    return Intl.message(
      'Check-out successful',
      name: 'checkOutSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Check-in failed`
  String get checkInFailed {
    return Intl.message(
      'Check-in failed',
      name: 'checkInFailed',
      desc: '',
      args: [],
    );
  }

  /// `Check-out failed`
  String get checkOutFailed {
    return Intl.message(
      'Check-out failed',
      name: 'checkOutFailed',
      desc: '',
      args: [],
    );
  }

  /// `Summary`
  String get summary {
    return Intl.message('Summary', name: 'summary', desc: '', args: []);
  }

  /// `Settings`
  String get settings {
    return Intl.message('Settings', name: 'settings', desc: '', args: []);
  }

  /// `Theme`
  String get theme {
    return Intl.message('Theme', name: 'theme', desc: '', args: []);
  }

  /// `Language`
  String get language {
    return Intl.message('Language', name: 'language', desc: '', args: []);
  }

  /// `System Setting`
  String get systemSetting {
    return Intl.message(
      'System Setting',
      name: 'systemSetting',
      desc: '',
      args: [],
    );
  }

  /// `Light`
  String get light {
    return Intl.message('Light', name: 'light', desc: '', args: []);
  }

  /// `Dark`
  String get dark {
    return Intl.message('Dark', name: 'dark', desc: '', args: []);
  }

  /// `Profile`
  String get profile {
    return Intl.message('Profile', name: 'profile', desc: '', args: []);
  }

  /// `Edit Profile`
  String get editProfile {
    return Intl.message(
      'Edit Profile',
      name: 'editProfile',
      desc: '',
      args: [],
    );
  }

  /// `Mobile`
  String get mobile {
    return Intl.message('Mobile', name: 'mobile', desc: '', args: []);
  }

  /// `Email`
  String get email {
    return Intl.message('Email', name: 'email', desc: '', args: []);
  }

  /// `Profile Picture`
  String get profilePicture {
    return Intl.message(
      'Profile Picture',
      name: 'profilePicture',
      desc: '',
      args: [],
    );
  }

  /// `Update Profile`
  String get updateProfile {
    return Intl.message(
      'Update Profile',
      name: 'updateProfile',
      desc: '',
      args: [],
    );
  }

  /// `Profile updated successfully`
  String get profileUpdatedSuccessfully {
    return Intl.message(
      'Profile updated successfully',
      name: 'profileUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Leaves`
  String get leaves {
    return Intl.message('Leaves', name: 'leaves', desc: '', args: []);
  }

  /// `Leave Requests`
  String get leaveRequests {
    return Intl.message(
      'Leave Requests',
      name: 'leaveRequests',
      desc: '',
      args: [],
    );
  }

  /// `Add Leave Request`
  String get addLeaveRequest {
    return Intl.message(
      'Add Leave Request',
      name: 'addLeaveRequest',
      desc: '',
      args: [],
    );
  }

  /// `Edit Leave Request`
  String get editLeaveRequest {
    return Intl.message(
      'Edit Leave Request',
      name: 'editLeaveRequest',
      desc: '',
      args: [],
    );
  }

  /// `Leave Type`
  String get leaveType {
    return Intl.message('Leave Type', name: 'leaveType', desc: '', args: []);
  }

  /// `From Date`
  String get fromDate {
    return Intl.message('From Date', name: 'fromDate', desc: '', args: []);
  }

  /// `To Date`
  String get toDate {
    return Intl.message('To Date', name: 'toDate', desc: '', args: []);
  }

  /// `Reason`
  String get reason {
    return Intl.message('Reason', name: 'reason', desc: '', args: []);
  }

  /// `Vacation Leave`
  String get vacationLeave {
    return Intl.message(
      'Vacation Leave',
      name: 'vacationLeave',
      desc: '',
      args: [],
    );
  }

  /// `Sick Leave`
  String get sickLeave {
    return Intl.message('Sick Leave', name: 'sickLeave', desc: '', args: []);
  }

  /// `Request Leave`
  String get requestLeave {
    return Intl.message(
      'Request Leave',
      name: 'requestLeave',
      desc: '',
      args: [],
    );
  }

  /// `Leave request added successfully`
  String get leaveRequestAdded {
    return Intl.message(
      'Leave request added successfully',
      name: 'leaveRequestAdded',
      desc: '',
      args: [],
    );
  }

  /// `Leave request updated successfully`
  String get leaveRequestUpdated {
    return Intl.message(
      'Leave request updated successfully',
      name: 'leaveRequestUpdated',
      desc: '',
      args: [],
    );
  }

  /// `Leave request deleted successfully`
  String get leaveRequestDeleted {
    return Intl.message(
      'Leave request deleted successfully',
      name: 'leaveRequestDeleted',
      desc: '',
      args: [],
    );
  }

  /// `Delete Leave Request`
  String get deleteLeaveRequest {
    return Intl.message(
      'Delete Leave Request',
      name: 'deleteLeaveRequest',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this leave request?`
  String get areYouSureDeleteLeave {
    return Intl.message(
      'Are you sure you want to delete this leave request?',
      name: 'areYouSureDeleteLeave',
      desc: '',
      args: [],
    );
  }

  /// `Clients`
  String get clients {
    return Intl.message('Clients', name: 'clients', desc: '', args: []);
  }

  /// `Add Client`
  String get addClient {
    return Intl.message('Add Client', name: 'addClient', desc: '', args: []);
  }

  /// `Company Name`
  String get companyName {
    return Intl.message(
      'Company Name',
      name: 'companyName',
      desc: '',
      args: [],
    );
  }

  /// `Responsible Name`
  String get responsibleName {
    return Intl.message(
      'Responsible Name',
      name: 'responsibleName',
      desc: '',
      args: [],
    );
  }

  /// `Responsible Phone`
  String get responsiblePhone {
    return Intl.message(
      'Responsible Phone',
      name: 'responsiblePhone',
      desc: '',
      args: [],
    );
  }

  /// `Responsible Job`
  String get responsibleJob {
    return Intl.message(
      'Responsible Job',
      name: 'responsibleJob',
      desc: '',
      args: [],
    );
  }

  /// `Responsible Email`
  String get responsibleEmail {
    return Intl.message(
      'Responsible Email',
      name: 'responsibleEmail',
      desc: '',
      args: [],
    );
  }

  /// `Client Status`
  String get clientStatus {
    return Intl.message(
      'Client Status',
      name: 'clientStatus',
      desc: '',
      args: [],
    );
  }

  /// `Leads`
  String get leads {
    return Intl.message('Leads', name: 'leads', desc: '', args: []);
  }

  /// `Client added successfully`
  String get clientAdded {
    return Intl.message(
      'Client added successfully',
      name: 'clientAdded',
      desc: '',
      args: [],
    );
  }

  /// `Approved`
  String get approved {
    return Intl.message('Approved', name: 'approved', desc: '', args: []);
  }

  /// `Pending`
  String get pending {
    return Intl.message('Pending', name: 'pending', desc: '', args: []);
  }

  /// `Rejected`
  String get rejected {
    return Intl.message('Rejected', name: 'rejected', desc: '', args: []);
  }

  /// `Issued At`
  String get issuedAt {
    return Intl.message('Issued At', name: 'issuedAt', desc: '', args: []);
  }

  /// `Select Date`
  String get selectDate {
    return Intl.message('Select Date', name: 'selectDate', desc: '', args: []);
  }

  /// `Select Leave Type`
  String get selectLeaveType {
    return Intl.message(
      'Select Leave Type',
      name: 'selectLeaveType',
      desc: '',
      args: [],
    );
  }

  /// `Reason cannot be empty`
  String get reasonCannotBeEmpty {
    return Intl.message(
      'Reason cannot be empty',
      name: 'reasonCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `From date cannot be empty`
  String get fromDateCannotBeEmpty {
    return Intl.message(
      'From date cannot be empty',
      name: 'fromDateCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `To date cannot be empty`
  String get toDateCannotBeEmpty {
    return Intl.message(
      'To date cannot be empty',
      name: 'toDateCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Company name cannot be empty`
  String get companyNameCannotBeEmpty {
    return Intl.message(
      'Company name cannot be empty',
      name: 'companyNameCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Responsible name cannot be empty`
  String get responsibleNameCannotBeEmpty {
    return Intl.message(
      'Responsible name cannot be empty',
      name: 'responsibleNameCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Responsible phone cannot be empty`
  String get responsiblePhoneCannotBeEmpty {
    return Intl.message(
      'Responsible phone cannot be empty',
      name: 'responsiblePhoneCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Tickets`
  String get tickets {
    return Intl.message('Tickets', name: 'tickets', desc: '', args: []);
  }

  /// `Contracts`
  String get contracts {
    return Intl.message('Contracts', name: 'contracts', desc: '', args: []);
  }

  /// `Near Expire`
  String get nearExpire {
    return Intl.message('Near Expire', name: 'nearExpire', desc: '', args: []);
  }

  /// `Expired`
  String get expired {
    return Intl.message('Expired', name: 'expired', desc: '', args: []);
  }

  /// `Agreements`
  String get agreements {
    return Intl.message('Agreements', name: 'agreements', desc: '', args: []);
  }

  /// `Quotations`
  String get quotations {
    return Intl.message('Quotations', name: 'quotations', desc: '', args: []);
  }

  /// `Quotations List`
  String get quotationsList {
    return Intl.message(
      'Quotations List',
      name: 'quotationsList',
      desc: '',
      args: [],
    );
  }

  /// `Save Quotation`
  String get saveQuotation {
    return Intl.message(
      'Save Quotation',
      name: 'saveQuotation',
      desc: '',
      args: [],
    );
  }

  /// `Edit Quotation`
  String get editQuotation {
    return Intl.message(
      'Edit Quotation',
      name: 'editQuotation',
      desc: '',
      args: [],
    );
  }

  /// `Licenses`
  String get licenses {
    return Intl.message('Licenses', name: 'licenses', desc: '', args: []);
  }

  /// `Subscriptions`
  String get subscriptions {
    return Intl.message(
      'Subscriptions',
      name: 'subscriptions',
      desc: '',
      args: [],
    );
  }

  /// `Maintenance`
  String get maintenance {
    return Intl.message('Maintenance', name: 'maintenance', desc: '', args: []);
  }

  /// `Help Center`
  String get helpCenter {
    return Intl.message('Help Center', name: 'helpCenter', desc: '', args: []);
  }

  /// `My Tickets`
  String get myTickets {
    return Intl.message('My Tickets', name: 'myTickets', desc: '', args: []);
  }

  /// `Archived Tickets`
  String get archivedTickets {
    return Intl.message(
      'Archived Tickets',
      name: 'archivedTickets',
      desc: '',
      args: [],
    );
  }

  /// `All Archived Tickets`
  String get allArchivedTickets {
    return Intl.message(
      'All Archived Tickets',
      name: 'allArchivedTickets',
      desc: '',
      args: [],
    );
  }

  /// `Software Management`
  String get softwareManagement {
    return Intl.message(
      'Software Management',
      name: 'softwareManagement',
      desc: '',
      args: [],
    );
  }

  /// `Clients List`
  String get clientsList {
    return Intl.message(
      'Clients List',
      name: 'clientsList',
      desc: '',
      args: [],
    );
  }

  /// `Meetings`
  String get meetings {
    return Intl.message('Meetings', name: 'meetings', desc: '', args: []);
  }

  /// `Client Opinions`
  String get clientOpinions {
    return Intl.message(
      'Client Opinions',
      name: 'clientOpinions',
      desc: '',
      args: [],
    );
  }

  /// `Company Registration`
  String get companyRegistration {
    return Intl.message(
      'Company Registration',
      name: 'companyRegistration',
      desc: '',
      args: [],
    );
  }

  /// `Client ID`
  String get clientID {
    return Intl.message('Client ID', name: 'clientID', desc: '', args: []);
  }

  /// `Created Date`
  String get createdDate {
    return Intl.message(
      'Created Date',
      name: 'createdDate',
      desc: '',
      args: [],
    );
  }

  /// `Validity Days`
  String get validityDays {
    return Intl.message(
      'Validity Days',
      name: 'validityDays',
      desc: '',
      args: [],
    );
  }

  /// `Product ID`
  String get productId {
    return Intl.message('Product ID', name: 'productId', desc: '', args: []);
  }

  /// `Sub Product ID`
  String get subProductId {
    return Intl.message(
      'Sub Product ID',
      name: 'subProductId',
      desc: '',
      args: [],
    );
  }

  /// `Has Limit Users`
  String get hasLimitUsers {
    return Intl.message(
      'Has Limit Users',
      name: 'hasLimitUsers',
      desc: '',
      args: [],
    );
  }

  /// `Quotation Code`
  String get quotationCode {
    return Intl.message(
      'Quotation Code',
      name: 'quotationCode',
      desc: '',
      args: [],
    );
  }

  /// `Quotation Status`
  String get quotationStatus {
    return Intl.message(
      'Quotation Status',
      name: 'quotationStatus',
      desc: '',
      args: [],
    );
  }

  /// `Service Name`
  String get serviceName {
    return Intl.message(
      'Service Name',
      name: 'serviceName',
      desc: '',
      args: [],
    );
  }

  /// `Sub Service Name`
  String get subServiceName {
    return Intl.message(
      'Sub Service Name',
      name: 'subServiceName',
      desc: '',
      args: [],
    );
  }

  /// `Quotation saved successfully`
  String get quotationSaved {
    return Intl.message(
      'Quotation saved successfully',
      name: 'quotationSaved',
      desc: '',
      args: [],
    );
  }

  /// `Select Client`
  String get selectClient {
    return Intl.message(
      'Select Client',
      name: 'selectClient',
      desc: '',
      args: [],
    );
  }

  /// `Select Product`
  String get selectProduct {
    return Intl.message(
      'Select Product',
      name: 'selectProduct',
      desc: '',
      args: [],
    );
  }

  /// `Select Sub Product`
  String get selectSubProduct {
    return Intl.message(
      'Select Sub Product',
      name: 'selectSubProduct',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get retry {
    return Intl.message('Retry', name: 'retry', desc: '', args: []);
  }

  /// `Select Products & Services`
  String get selectProductsAndServices {
    return Intl.message(
      'Select Products & Services',
      name: 'selectProductsAndServices',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get done {
    return Intl.message('Done', name: 'done', desc: '', args: []);
  }

  /// `No products available`
  String get noProductsAvailable {
    return Intl.message(
      'No products available',
      name: 'noProductsAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load products`
  String get failedToLoadProducts {
    return Intl.message(
      'Failed to load products',
      name: 'failedToLoadProducts',
      desc: '',
      args: [],
    );
  }

  /// `Select Sub-Services`
  String get selectSubServices {
    return Intl.message(
      'Select Sub-Services',
      name: 'selectSubServices',
      desc: '',
      args: [],
    );
  }

  /// `Cost`
  String get cost {
    return Intl.message('Cost', name: 'cost', desc: '', args: []);
  }

  /// `Has User Limit`
  String get hasUserLimit {
    return Intl.message(
      'Has User Limit',
      name: 'hasUserLimit',
      desc: '',
      args: [],
    );
  }

  /// `products selected`
  String get productsSelected {
    return Intl.message(
      'products selected',
      name: 'productsSelected',
      desc: '',
      args: [],
    );
  }

  /// `product selected`
  String get productSelected {
    return Intl.message(
      'product selected',
      name: 'productSelected',
      desc: '',
      args: [],
    );
  }

  /// `sub-services selected`
  String get subServicesSelected {
    return Intl.message(
      'sub-services selected',
      name: 'subServicesSelected',
      desc: '',
      args: [],
    );
  }

  /// `sub-service selected`
  String get subServiceSelected {
    return Intl.message(
      'sub-service selected',
      name: 'subServiceSelected',
      desc: '',
      args: [],
    );
  }

  /// `Please select at least one product`
  String get pleaseSelectAtLeastOneProduct {
    return Intl.message(
      'Please select at least one product',
      name: 'pleaseSelectAtLeastOneProduct',
      desc: '',
      args: [],
    );
  }

  /// `Please select at least one sub-product`
  String get pleaseSelectAtLeastOneSubProduct {
    return Intl.message(
      'Please select at least one sub-product',
      name: 'pleaseSelectAtLeastOneSubProduct',
      desc: '',
      args: [],
    );
  }

  /// `Please select a client first`
  String get pleaseSelectClientFirst {
    return Intl.message(
      'Please select a client first',
      name: 'pleaseSelectClientFirst',
      desc: '',
      args: [],
    );
  }

  /// `Loading clients...`
  String get loadingClients {
    return Intl.message(
      'Loading clients...',
      name: 'loadingClients',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load clients`
  String get failedToLoadClients {
    return Intl.message(
      'Failed to load clients',
      name: 'failedToLoadClients',
      desc: '',
      args: [],
    );
  }

  /// `My Profile`
  String get myProfile {
    return Intl.message('My Profile', name: 'myProfile', desc: '', args: []);
  }

  /// `Services`
  String get services {
    return Intl.message('Services', name: 'services', desc: '', args: []);
  }

  /// `Sales`
  String get sales {
    return Intl.message('Sales', name: 'sales', desc: '', args: []);
  }

  /// `Customers`
  String get customers {
    return Intl.message('Customers', name: 'customers', desc: '', args: []);
  }

  /// `Customers List`
  String get customersList {
    return Intl.message(
      'Customers List',
      name: 'customersList',
      desc: '',
      args: [],
    );
  }

  /// `Customer Meetings`
  String get customerMeetings {
    return Intl.message(
      'Customer Meetings',
      name: 'customerMeetings',
      desc: '',
      args: [],
    );
  }

  /// `Customer Opinions`
  String get customerOpinions {
    return Intl.message(
      'Customer Opinions',
      name: 'customerOpinions',
      desc: '',
      args: [],
    );
  }

  /// `Licenses & Subscriptions`
  String get licensesSubscriptions {
    return Intl.message(
      'Licenses & Subscriptions',
      name: 'licensesSubscriptions',
      desc: '',
      args: [],
    );
  }

  /// `Active Tickets`
  String get activeTickets {
    return Intl.message(
      'Active Tickets',
      name: 'activeTickets',
      desc: '',
      args: [],
    );
  }

  /// `Finished Tickets`
  String get finishedTickets {
    return Intl.message(
      'Finished Tickets',
      name: 'finishedTickets',
      desc: '',
      args: [],
    );
  }

  /// `Near To Expire`
  String get nearToExpire {
    return Intl.message(
      'Near To Expire',
      name: 'nearToExpire',
      desc: '',
      args: [],
    );
  }

  /// `Has Expired`
  String get hasExpired {
    return Intl.message('Has Expired', name: 'hasExpired', desc: '', args: []);
  }

  /// `Sub Products`
  String get subProducts {
    return Intl.message(
      'Sub Products',
      name: 'subProducts',
      desc: '',
      args: [],
    );
  }

  /// `Home Updates`
  String get homeUpdates {
    return Intl.message(
      'Home Updates',
      name: 'homeUpdates',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get next {
    return Intl.message('Next', name: 'next', desc: '', args: []);
  }

  /// `Previous`
  String get previous {
    return Intl.message('Previous', name: 'previous', desc: '', args: []);
  }

  /// `Select Parent Product`
  String get selectParentProduct {
    return Intl.message(
      'Select Parent Product',
      name: 'selectParentProduct',
      desc: '',
      args: [],
    );
  }

  /// `Select Sub Products`
  String get selectSubProducts {
    return Intl.message(
      'Select Sub Products',
      name: 'selectSubProducts',
      desc: '',
      args: [],
    );
  }

  /// `Finish`
  String get finish {
    return Intl.message('Finish', name: 'finish', desc: '', args: []);
  }

  /// `Step`
  String get step {
    return Intl.message('Step', name: 'step', desc: '', args: []);
  }

  /// `of`
  String get of1 {
    return Intl.message('of', name: 'of1', desc: '', args: []);
  }

  /// `Client Selection`
  String get clientSelection {
    return Intl.message(
      'Client Selection',
      name: 'clientSelection',
      desc: '',
      args: [],
    );
  }

  /// `My Archived Tickets`
  String get myArchivedTickets {
    return Intl.message(
      'My Archived Tickets',
      name: 'myArchivedTickets',
      desc: '',
      args: [],
    );
  }

  /// `Product Selection`
  String get productSelection {
    return Intl.message(
      'Product Selection',
      name: 'productSelection',
      desc: '',
      args: [],
    );
  }

  /// `Sub Product Selection`
  String get subProductSelection {
    return Intl.message(
      'Sub Product Selection',
      name: 'subProductSelection',
      desc: '',
      args: [],
    );
  }

  /// `Search by code or title`
  String get searchByCodeOrTitle {
    return Intl.message(
      'Search by code or title',
      name: 'searchByCodeOrTitle',
      desc: '',
      args: [],
    );
  }

  /// `Employee Name`
  String get empName {
    return Intl.message('Employee Name', name: 'empName', desc: '', args: []);
  }

  /// `Job Title`
  String get empJob {
    return Intl.message('Job Title', name: 'empJob', desc: '', args: []);
  }

  /// `User Limit`
  String get userLimit {
    return Intl.message('User Limit', name: 'userLimit', desc: '', args: []);
  }

  /// `Enter user limit`
  String get enterUserLimit {
    return Intl.message(
      'Enter user limit',
      name: 'enterUserLimit',
      desc: '',
      args: [],
    );
  }

  /// `Users`
  String get users {
    return Intl.message('Users', name: 'users', desc: '', args: []);
  }

  /// `Quotation Details`
  String get quotationDetails {
    return Intl.message(
      'Quotation Details',
      name: 'quotationDetails',
      desc: '',
      args: [],
    );
  }

  /// `Client Information`
  String get clientInformation {
    return Intl.message(
      'Client Information',
      name: 'clientInformation',
      desc: '',
      args: [],
    );
  }

  /// `Status History`
  String get statusHistory {
    return Intl.message(
      'Status History',
      name: 'statusHistory',
      desc: '',
      args: [],
    );
  }

  /// `Actions`
  String get actions {
    return Intl.message('Actions', name: 'actions', desc: '', args: []);
  }

  /// `Created At`
  String get createdAt {
    return Intl.message('Created At', name: 'createdAt', desc: '', args: []);
  }

  /// `Created By`
  String get createdBy {
    return Intl.message('Created By', name: 'createdBy', desc: '', args: []);
  }

  /// `VAT`
  String get vat {
    return Intl.message('VAT', name: 'vat', desc: '', args: []);
  }

  /// `Edit`
  String get edit {
    return Intl.message('Edit', name: 'edit', desc: '', args: []);
  }

  /// `Price`
  String get price {
    return Intl.message('Price', name: 'price', desc: '', args: []);
  }

  /// `Print`
  String get print {
    return Intl.message('Print', name: 'print', desc: '', args: []);
  }

  /// `Send`
  String get send {
    return Intl.message('Send', name: 'send', desc: '', args: []);
  }

  /// `Quotation Pricing`
  String get quotationPricing {
    return Intl.message(
      'Quotation Pricing',
      name: 'quotationPricing',
      desc: '',
      args: [],
    );
  }

  /// `Services Pricing`
  String get servicesPricing {
    return Intl.message(
      'Services Pricing',
      name: 'servicesPricing',
      desc: '',
      args: [],
    );
  }

  /// `Extra User Cost`
  String get extraUserCost {
    return Intl.message(
      'Extra User Cost',
      name: 'extraUserCost',
      desc: '',
      args: [],
    );
  }

  /// `Enter Cost`
  String get enterCost {
    return Intl.message('Enter Cost', name: 'enterCost', desc: '', args: []);
  }

  /// `Sub Services`
  String get subServices {
    return Intl.message(
      'Sub Services',
      name: 'subServices',
      desc: '',
      args: [],
    );
  }

  /// `Payment Details`
  String get paymentDetails {
    return Intl.message(
      'Payment Details',
      name: 'paymentDetails',
      desc: '',
      args: [],
    );
  }

  /// `Initial Amount`
  String get initialAmount {
    return Intl.message(
      'Initial Amount',
      name: 'initialAmount',
      desc: '',
      args: [],
    );
  }

  /// `Discount`
  String get discount {
    return Intl.message('Discount', name: 'discount', desc: '', args: []);
  }

  /// `Enter Discount`
  String get enterDiscount {
    return Intl.message(
      'Enter Discount',
      name: 'enterDiscount',
      desc: '',
      args: [],
    );
  }

  /// `Payment Installments`
  String get paymentInstallments {
    return Intl.message(
      'Payment Installments',
      name: 'paymentInstallments',
      desc: '',
      args: [],
    );
  }

  /// `Enter Installments`
  String get enterInstallments {
    return Intl.message(
      'Enter Installments',
      name: 'enterInstallments',
      desc: '',
      args: [],
    );
  }

  /// `Payment Start Date`
  String get paymentStartDate {
    return Intl.message(
      'Payment Start Date',
      name: 'paymentStartDate',
      desc: '',
      args: [],
    );
  }

  /// `Payment Every`
  String get paymentEvery {
    return Intl.message(
      'Payment Every',
      name: 'paymentEvery',
      desc: '',
      args: [],
    );
  }

  /// `Months`
  String get months {
    return Intl.message('Months', name: 'months', desc: '', args: []);
  }

  /// `Enter Months`
  String get enterMonths {
    return Intl.message(
      'Enter Months',
      name: 'enterMonths',
      desc: '',
      args: [],
    );
  }

  /// `Save Pricing`
  String get savePricing {
    return Intl.message(
      'Save Pricing',
      name: 'savePricing',
      desc: '',
      args: [],
    );
  }

  /// `Pricing saved successfully`
  String get pricingSavedSuccessfully {
    return Intl.message(
      'Pricing saved successfully',
      name: 'pricingSavedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to save pricing`
  String get failedToSavePricing {
    return Intl.message(
      'Failed to save pricing',
      name: 'failedToSavePricing',
      desc: '',
      args: [],
    );
  }

  /// `Quotation Summary`
  String get quotationSummary {
    return Intl.message(
      'Quotation Summary',
      name: 'quotationSummary',
      desc: '',
      args: [],
    );
  }

  /// `Selected Client`
  String get selectedClient {
    return Intl.message(
      'Selected Client',
      name: 'selectedClient',
      desc: '',
      args: [],
    );
  }

  /// `Selected Products`
  String get selectedProducts {
    return Intl.message(
      'Selected Products',
      name: 'selectedProducts',
      desc: '',
      args: [],
    );
  }

  /// `Note`
  String get note {
    return Intl.message('Note', name: 'note', desc: '', args: []);
  }

  /// `Please review all the information above before submitting the quotation. Once submitted, you can modify pricing and other details later.`
  String get quotationSummaryNote {
    return Intl.message(
      'Please review all the information above before submitting the quotation. Once submitted, you can modify pricing and other details later.',
      name: 'quotationSummaryNote',
      desc: '',
      args: [],
    );
  }

  /// `No sub-products selected`
  String get noSubProductsSelected {
    return Intl.message(
      'No sub-products selected',
      name: 'noSubProductsSelected',
      desc: '',
      args: [],
    );
  }

  /// `Services/Products`
  String get servicesTab {
    return Intl.message(
      'Services/Products',
      name: 'servicesTab',
      desc: '',
      args: [],
    );
  }

  /// `History`
  String get historyTab {
    return Intl.message('History', name: 'historyTab', desc: '', args: []);
  }

  /// `User Name`
  String get userName {
    return Intl.message('User Name', name: 'userName', desc: '', args: []);
  }

  /// `<EMAIL>`
  String get userEmail {
    return Intl.message(
      '<EMAIL>',
      name: 'userEmail',
      desc: '',
      args: [],
    );
  }

  /// `Approve Client`
  String get approveClient {
    return Intl.message(
      'Approve Client',
      name: 'approveClient',
      desc: '',
      args: [],
    );
  }

  /// `Issue License`
  String get issueLicense {
    return Intl.message(
      'Issue License',
      name: 'issueLicense',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to`
  String get areYouSureYouWantTo {
    return Intl.message(
      'Are you sure you want to',
      name: 'areYouSureYouWantTo',
      desc: '',
      args: [],
    );
  }

  /// `Status updated successfully`
  String get statusUpdatedSuccessfully {
    return Intl.message(
      'Status updated successfully',
      name: 'statusUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Pricing`
  String get pricing {
    return Intl.message('Pricing', name: 'pricing', desc: '', args: []);
  }

  /// `Client Approval`
  String get clientApproval {
    return Intl.message(
      'Client Approval',
      name: 'clientApproval',
      desc: '',
      args: [],
    );
  }

  /// `Client Note`
  String get clientNote {
    return Intl.message('Client Note', name: 'clientNote', desc: '', args: []);
  }

  /// `Arabic Print`
  String get arabicPrint {
    return Intl.message(
      'Arabic Print',
      name: 'arabicPrint',
      desc: '',
      args: [],
    );
  }

  /// `English Print`
  String get englishPrint {
    return Intl.message(
      'English Print',
      name: 'englishPrint',
      desc: '',
      args: [],
    );
  }

  /// `Select Print Language`
  String get selectPrintLanguage {
    return Intl.message(
      'Select Print Language',
      name: 'selectPrintLanguage',
      desc: '',
      args: [],
    );
  }

  /// `Add Note`
  String get addNote {
    return Intl.message('Add Note', name: 'addNote', desc: '', args: []);
  }

  /// `Add Attachment`
  String get addAttachment {
    return Intl.message(
      'Add Attachment',
      name: 'addAttachment',
      desc: '',
      args: [],
    );
  }

  /// `Note is required`
  String get noteRequired {
    return Intl.message(
      'Note is required',
      name: 'noteRequired',
      desc: '',
      args: [],
    );
  }

  /// `Attachment (Optional)`
  String get attachmentOptional {
    return Intl.message(
      'Attachment (Optional)',
      name: 'attachmentOptional',
      desc: '',
      args: [],
    );
  }

  /// `Update Status`
  String get updateStatus {
    return Intl.message(
      'Update Status',
      name: 'updateStatus',
      desc: '',
      args: [],
    );
  }

  /// `Failed to update status`
  String get failedToUpdateStatus {
    return Intl.message(
      'Failed to update status',
      name: 'failedToUpdateStatus',
      desc: '',
      args: [],
    );
  }

  /// `PDF generated successfully`
  String get pdfGeneratedSuccessfully {
    return Intl.message(
      'PDF generated successfully',
      name: 'pdfGeneratedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to generate PDF`
  String get failedToGeneratePdf {
    return Intl.message(
      'Failed to generate PDF',
      name: 'failedToGeneratePdf',
      desc: '',
      args: [],
    );
  }

  /// `Generating PDF...`
  String get generatingPdf {
    return Intl.message(
      'Generating PDF...',
      name: 'generatingPdf',
      desc: '',
      args: [],
    );
  }

  /// `Price Calculation`
  String get priceCalculation {
    return Intl.message(
      'Price Calculation',
      name: 'priceCalculation',
      desc: '',
      args: [],
    );
  }

  /// `Subtotal`
  String get subtotal {
    return Intl.message('Subtotal', name: 'subtotal', desc: '', args: []);
  }

  /// `Total`
  String get total {
    return Intl.message('Total', name: 'total', desc: '', args: []);
  }

  /// `Update Quotation`
  String get updateQuotation {
    return Intl.message(
      'Update Quotation',
      name: 'updateQuotation',
      desc: '',
      args: [],
    );
  }

  /// `Quotation updated successfully`
  String get quotationUpdatedSuccessfully {
    return Intl.message(
      'Quotation updated successfully',
      name: 'quotationUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to update quotation`
  String get failedToUpdateQuotation {
    return Intl.message(
      'Failed to update quotation',
      name: 'failedToUpdateQuotation',
      desc: '',
      args: [],
    );
  }

  /// `Please complete all steps`
  String get pleaseCompleteAllSteps {
    return Intl.message(
      'Please complete all steps',
      name: 'pleaseCompleteAllSteps',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
